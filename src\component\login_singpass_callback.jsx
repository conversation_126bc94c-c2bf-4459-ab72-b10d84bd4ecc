//Library imports
import { useEffect } from "react";
import { <PERSON>, Container, Row } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { PulseLoader } from "react-spinners";

//Design imports
import "../style.css";
import axios from "axios";
import { BackendRoute, FrontendRoute } from "../data/route";
import { useCustomerStore } from "../customer_store";

export default function LoginSingpassCallback() {
  const navigate = useNavigate();
  const {
    setUserEmail,
    setUserNameFirst,
    setUserNameLast,
    setUserNumberMobile,
    setUserNric,
  } = useCustomerStore();

  useEffect(() => {
    const query = new URLSearchParams(window.location.search);
    const code = query.get("code");
    const state = query.get("state");

    if (!code || !state) {
      navigate("/"); // Invalid callback
      return;
    }

    // Exchange code for tokens via backend Lambda
    axios
      .post(BackendRoute.singpass.callback, { code, state })
      .then((response) => {
        if (response.data && response.data.statusCode === 200) {
          // Save user session
          localStorage.setItem("user", response.data.body);
          const { userInfo } = JSON.parse(response.data.body);
          if (userInfo) {
            if (Object.prototype.hasOwnProperty("email"))
              setUserEmail(userInfo.email.value);
            if (Object.prototype.hasOwnProperty("email"))
              setUserEmail(userInfo.email.value);
            if (Object.prototype.hasOwnProperty("email"))
              setUserEmail(userInfo.email.value);

            navigate(FrontendRoute.register.detail);
          } else {
            throw new Error("Failed to retrieve user info");
          }
        } else {
          navigate("/");
        }
      })
      .catch((error) => {
        //Log
        console.log(error);
        navigate("/");
      });
  }, [navigate]);

  //Return element
  return (
    <Container className="register-loading-container">
      <Row className="pb-5">
        <Col className="align-items-center d-flex justify-content-center">
          <PulseLoader
            color="#0055B8"
            loading={true}
            size={25}
            speedMultiplier={0.8}
          />
        </Col>
      </Row>
      <Row className="align-items-center d-flex justify-content-center">
        <label className="register-loading-container-text">
          Logging you in via Singpass...
        </label>
      </Row>
    </Container>
  );
}
