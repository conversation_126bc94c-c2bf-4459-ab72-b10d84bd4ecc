////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		account_update.jsx
*	\brief		Account update page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	21 Jun'24 : darytltan - added function
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import { useEffect , useState } from "react";
import { Button , Col , Container, Row } from "react-bootstrap"
import { IoIosArrowBack } from "react-icons/io";
import { useLocation , useNavigate } from "react-router-dom";

//Internal imports
import * as config from '../config'
import { useCustomerStore } from "../customer_store";
import { FrontendRoute } from "../data/route";

export default function AccountUpdate()
{
	/* Variables */

	//Hooks
	const m_location = useLocation()
	const m_navigate = useNavigate()

	//States
	const [m_currentPath , set_currentPath] = useState( '' )

	//State hooks
	const { session_id } = useCustomerStore()

	/* Functions */

	useEffect
	(	() =>
		{
			//Get current update path
			let temp_path_current = String( m_location.pathname ).split( '/' ).at( -1 )
			
			//Set current path for tracking 
			set_currentPath( temp_path_current )
		}
	,	[m_location]
	)

	function HandleBackButton()
	{
		//Navigate to account page
		m_navigate( FrontendRoute.account.session + '/' + String( session_id ) )
	}

	function RenderHeaderText()
	{
		switch( m_currentPath )
		{
			//Case for contact
			case config.frontend.route.account.contact :
			{
				//Return element
				return <label className="login-session-update-header-text text-center">Edit Mobile Number</label>
			}//end case

			//Case for payment
			case config.frontend.route.account.payment :
			{
				//Return element
				return <label className="login-session-update-header-text text-center">Edit Payment Details</label>
			}//end case

			//Default case
			default : return <></>
		}//end switch
	}

	return (
		<>
			<Container>
				<Row>
					<Col className="align-items-center d-flex justify-content-start">
						<Button className="login-session-update-header-back-button" onClick={HandleBackButton}>
							<IoIosArrowBack color="#0055B8" size={24} />
						</Button>
					</Col>
					<Col className="align-items-center d-flex justify-content-center" xs='auto'>
						<RenderHeaderText />
					</Col>
					<Col></Col>
				</Row>
			</Container>
		</>
	)	
}