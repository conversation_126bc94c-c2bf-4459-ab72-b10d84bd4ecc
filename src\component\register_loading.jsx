//Library imports
import { useEffect, useState } from 'react'
import { Col, Container, Row } from 'react-bootstrap'
import { useLocation } from 'react-router-dom'
import { PulseLoader } from 'react-spinners'

//Internal imports
import { FrontendRoute } from '../data/route'

//Design imports
import '../style.css'

export default function RegisterLoading() {
    /* Variables */

    //Hooks
    const m_location = useLocation()

    //States
    const [m_currentPath, set_currentPath] = useState('')

    /* Functions */

    useEffect
    (   () => 
        {
            //Set current path for tracking
            set_currentPath(m_location.pathname)
        }
    ,  [m_location]
    )

    function RenderLoadingPage() 
    {
        return (
            <>
                <Container className='register-loading-container'>
                    <Row className='pb-5'>
                        <Col className='align-items-center d-flex justify-content-center'>
                            <PulseLoader color='#0055B8' loading={true} size={25} speedMultiplier={0.8} />
                        </Col>
                    </Row>
                    <Row className='align-items-center d-flex justify-content-center'>
                        <RenderLoadingText />
                    </Row>
                </Container>
            </>

        )
    }

    function RenderLoadingText() 
    {
        switch (m_currentPath) 
        {
            //Case for homepage
            case '/':
            {
                return <label className='register-loading-container-text'>Starting registration...</label>
            }

            //Case for detail route
            case FrontendRoute.register.detail:
            {
                return <label className='register-loading-container-text'>Processing your details...</label>
            }

            //Case for payment route
            case FrontendRoute.register.payment:
            {
                return <label className='register-loading-container-text'>Processing your payment details...</label>
            }

            //Case for palm route
            case FrontendRoute.register.palm:
            {
                return <label className='register-loading-container-text'>Waiting for palm scan...</label>
            }

            //Default case
            default:
                return <></>
        }
    }

    return (
        <>
            <RenderLoadingPage />
        </>
    )
}