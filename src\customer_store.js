//Library imports
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export const useCustomerStore = create(
  persist(
    (set) => ({
      //Initial values for personal info
      user_email: "",
      user_name_first: "",
      user_name_last: "",
      user_number_mobile: "",
      user_nric: "",

      //Initial values for Stripe
      stripe_customer_id: "",
      stripe_payment_brand: "",
      stripe_payment_expiry: "",
      stripe_payment_last4: "",
      stripe_secret: "",

      //Biometric Scan Result
      palm_scan_result: null,

      //Reset store to defaults
      resetStore: () =>
        set({
          user_email: "",
          user_name_first: "",
          user_name_last: "",
          user_number_mobile: "",
          user_nric: "",
          stripe_customer_id: "",
          stripe_payment_brand: "",
          stripe_payment_expiry: "",
          stripe_payment_last4: "",
          stripe_secret: "",
          palm_scan_result: null,
        }),

      //Setters for personal info
      setUserEmail: (email) => set({ user_email: email }),
      setUserNameFirst: (first) => set({ user_name_first: first }),
      setUserNameLast: (last) => set({ user_name_last: last }),
      setUserNumberMobile: (mobile) => set({ user_number_mobile: mobile }),
      setUserNric: (nric) => set({ user_nric: nric }),

      //Setter for Stripe
      setStripeCustomerId: (id) => set({ stripe_customer_id: id }),
      setStripePaymentBrand: (brand) => set({ stripe_payment_brand: brand }),
      setStripePaymentExpiry: (expiry) =>
        set({ stripe_payment_expiry: expiry }),
      setStripePaymentLast4: (last4) => set({ stripe_payment_last4: last4 }),
      setStripeSecret: (secret) => set({ stripe_secret: secret }),

      //Setter for scan result
      setPalmScanResult: (result) => set({ palm_scan_result: result }),
    }),
    {
      name: "palmpay-customer",
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
