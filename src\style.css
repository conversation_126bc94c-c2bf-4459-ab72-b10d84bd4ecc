/* Default styles */

body {
  color: black;
  font-family: "<PERSON>", sans-serif;
  font-size: 1.1em;
  font-style: normal;
  font-weight: 400;
}

/* Bootstrap styles */

.btn {
  background-color: #0055b8;
  border-radius: 14px;
  color: white;
  font-family: "<PERSON>", sans-serif;
  font-size: 2vh;
  font-style: normal;
  font-weight: 600;
  max-width: 100%;
  max-height: 100%;
  width: 85%;
  height: 6vh;
}

.container > .row {
  padding-top: 0.3em;
  padding-bottom: 0.3rem;
}

/* Standard styles */

.header-text {
  color: #0055b8;
  font-family: "<PERSON>", sans-serif;
  font-size: 2.4rem;
  font-style: normal;
  font-weight: 700;
}

/* Layout styles */

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.register-step-text {
  color: #222222;
  font-family: "<PERSON>", sans-serif;
  font-size: 1.4rem;
  font-style: normal;
  font-weight: 500;
}

/* Page styles */

.legal-body-container {
  background-color: white;
}

.login-facial-guideline-text {
  color: #e74f3d;
  font-family: "Barlow", sans-serif;
  font-size: 1.5vh;
}

.login-facial-retake-container {
  left: 50%;
  position: absolute;
  top: 85%;
  transform: translate(-50%, -50%);
}

.login-facial-status-container {
  left: 50%;
  position: absolute;
  top: 40%;
  transform: translate(-50%, -50%);
}

.login-facial-status-text {
  color: #0055b8;
  font-family: "Barlow", sans-serif;
  font-size: 2.4vh;
  font-weight: 600;
}

.login-session-body-edit-button {
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  color: #0055b8;
  font-family: "Barlow", sans-serif;
  font-size: 1.8vh;
  font-weight: 400;
  min-width: 100%;
  width: auto;
}

.login-session-body-edit-button:hover {
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  color: #0055b8;
  font-family: "Barlow", sans-serif;
  font-size: 1.8vh;
  font-weight: 400;
}

.login-session-body-account-header {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 2.2vh;
  font-weight: 600;
}

.login-session-body-user-image {
  max-width: 13vh;
  max-height: 13vh;
  object-fit: cover;
  overflow: hidden;
  width: auto;
  height: auto;
}

.login-session-body-user-name {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 3vh;
  font-weight: 700;
}

.login-session-exit-button {
  background-color: rgba(0, 0, 0, 0);
  border-color: rgba(0, 0, 0, 0);
  min-width: 7.5vh;
  width: auto;
}

.login-session-header-container {
  background: linear-gradient(94.35deg, #0055b8 -1.49%, #1fd2ff 111.73%);
  min-width: 100%;
  min-height: 11vh;
  width: auto;
  height: auto;
}

.login-session-header-container-text {
  color: white;
  font-family: "Barlow", sans-serif;
  font-size: 2.6vh;
  font-style: normal;
  font-weight: 600;
}

.login-session-update-header-back-button {
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  width: auto;
}

.login-session-update-header-back-button:hover {
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  width: auto;
}

.login-session-update-header-text {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 2vh;
  font-style: normal;
  font-weight: 500;
}

.login-session-update-body-header-text {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 3.2vh;
  font-style: normal;
  font-weight: 700;
}

.login-session-update-body-button-position {
  bottom: 10%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
}

.login-session-update-body-container-position {
  left: 50%;
  position: absolute;
  top: 40%;
  transform: translate(-50%, -50%);
}

.login-session-update-body-container-subheader-text {
  color: #0055b8;
  font-family: "Barlow", sans-serif;
  font-size: 2.4vh;
  font-style: normal;
  font-weight: 600;
}

.login-session-update-body-container-subtext-text {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 2vh;
  font-style: normal;
  font-weight: 400;
}

.login-session-update-body-edit-button {
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  color: #0055b8;
  font-family: "Barlow", sans-serif;
  font-size: 2vh;
  font-style: normal;
  font-weight: 400;
  width: auto;
  height: auto;
}

.login-session-update-body-edit-button:hover {
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  color: #0055b8;
  font-family: "Barlow", sans-serif;
  font-size: 2vh;
  font-style: normal;
  font-weight: 400;
  width: auto;
  height: auto;
}

.login-session-update-body-input-header {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 2vh;
  font-style: normal;
  font-weight: 600;
}

.login-session-update-body-input-field {
  border: 1px solid #8e8e8e;
  border-radius: 14px;
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 2vh;
  max-width: 100%;
  max-height: 100%;
  min-height: 6vh;
}

.login-session-update-body-input-field:disabled {
  background-color: rgba(0, 0, 0, 0);
}

.login-session-update-body-input-field:focus {
  border: 2px solid #0055b8;
  box-shadow: none;
}

.login-session-update-body-payment-field {
  border: 1px solid #8e8e8e;
  border-radius: 12px;
  min-height: 6vh;
  width: auto;
  height: auto;
}

.register-header-exit-button {
  background-color: rgba(0, 0, 0, 0);
  border-color: rgba(0, 0, 0, 0);
}

.register-detail-email-label {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 1.5vh;
  font-weight: 400;
  font-style: normal;
}

/*
.register-detail-form-input {
	border: 1px solid #8E8E8E;
	border-radius: 14px;
	color: #222222;
	font-family: "Barlow", sans-serif;
	font-size: 2vh;
	max-width: 100%;
	max-height: 100%;
	min-height: 6vh;
	width: auto;
	height: auto;
}

.register-detail-form-input:focus {
	border: 2px solid #0055B8;
	box-shadow: none;
}
*/

.register-facial-image-guideline-button {
  background-color: white;
  border: 0;
  color: #0055b8;
}

.register-facial-image-retake-button {
  background-color: white;
  border: 1px solid #8e8e8e;
  color: #222222;
}

.register-facial-button-capture {
  max-width: 50em;
}

.register-facial-button-guidelines {
  max-width: 50em;
}

.register-facial-image {
  border-radius: 24px;
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.register-facial-image-example {
  max-width: 25rem;
  max-height: 25rem;
  width: auto;
  height: auto;
}

.register-facial-retake-container {
  border: 2px solid #e74f3d;
  border-radius: 24px;
  max-width: 30rem;
  max-height: 30rem;
  position: relative;
  width: auto;
  height: auto;
}

.register-facial-retake-icon {
  left: 90%;
  position: absolute;
  top: 10%;
  transform: translate(-50%, -50%);
}

.register-facial-instructions-correct {
  border: 1.5px solid #22b573;
  border-radius: 18px;
  position: relative;
  width: fit-content;
  height: fit-content;
}

.register-facial-instructions-icon {
  left: 50%;
  position: absolute;
  top: 0%;
  transform: translate(-50%, -50%);
}

.register-facial-instructions-incorrect {
  border: 1.5px solid #c1272d;
  border-radius: 18px;
  position: relative;
}

.register-facial-confirm-text {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 1.6vh;
  font-weight: 400;
  font-style: normal;
  text-align: start;
}

.register-facial-webcam {
  max-width: 45rem;
  max-height: 45rem;
}

.register-loading-container {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}

.register-loading-container-text {
  color: #0055b8;
  font-family: "Barlow", sans-serif;
  font-size: 2.5vh;
  font-weight: 600;
  font-style: normal;
  text-align: center;
}

.register-login-button-singpass {
  background-color: #f4333d;
  border-color: white;
  border-radius: 8px;
  border-width: 1px;
  color: white;
  font-family: "Barlow", sans-serif;
  font-size: 2.5vh;
  font-weight: 500;
  font-style: normal;
  max-width: 50em;
}
.register-login-button-singpass > img {
  vertical-align: middle;
  height: 1.3em;
  margin-bottom: 0.2em;
}

.register-login-button-singpass:hover {
  background-color: #b0262d;
}
.register-login-button-singpass:active {
  background-color: #801e23;
}

.register-login-button-createAccount {
  background-color: rgba(0, 0, 0, 0);
  border-color: white;
  border-radius: 8px;
  border-width: 1px;
  color: white;
  font-family: "Barlow", sans-serif;
  font-size: 2.5vh;
  font-weight: 500;
  font-style: normal;
  max-width: 50em;
}

.register-login-button-faceLogin {
  background-color: #0055b8;
  border-color: rgba(0, 0, 0, 0);
  border-radius: 8px;
  color: white;
  font-family: "Barlow", sans-serif;
  font-size: 2.5vh;
  font-weight: 700;
  font-style: normal;
  max-width: 50em;
}

.register-login-container-header {
  background-color: rgb(255, 255, 255);
  min-width: 100vw;
  min-height: 100vh;
  width: auto;
  height: auto;

  /* 🆕 Center content horizontally and vertically */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  /* 🆕 Add padding to avoid edge clipping */
  padding-left: 1rem;
  padding-right: 1rem;
}

.register-login-container-body {
  width: 100%;
  max-width: 480px;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.register-login-footer-container {
  bottom: 0%;
  background-color: #ebf4ff;
  min-width: 100vw;
  min-height: 5vh;
  width: auto;
  height: auto;
}

.register-login-footer-text {
  color: black;
  font-family: "Barlow", sans-serif;
  font-size: 1.5vh;
  font-weight: 400;
  font-style: normal;
}

/*used*/
.register-login-header-logo {
  width: 40vw;
  max-width: 200px;
  height: auto;
  display: block;
  margin: 0 auto;
}

.register-login-header-text {
  color: white;
  font-family: "Barlow", sans-serif;
  font-size: 3.5vh;
  font-weight: 700;
  font-style: normal;
}

.register-summary-header-container {
  background: linear-gradient(180deg, #0055b8 25%, #1fd2ff 100%);
  min-width: 100%;
  min-height: 19vh;
  width: auto;
  height: auto;
}

.register-summary-header-container-image {
  left: 50%;
  position: absolute;
  top: 8%;
  transform: translate(-50%, -50%);
}

.register-summary-header-container-text {
  color: white;
  font-family: "Barlow", sans-serif;
  font-size: 2.6vh;
  font-style: normal;
  font-weight: 600;
  left: 50%;
  position: absolute;
  top: 17%;
  transform: translate(-50%, -50%);
}

.register-summary-details-content {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 1.9vh;
  font-style: normal;
  font-weight: 400;
}

.register-summary-details-email-verified {
  background-color: #cbffe0;
  max-width: fit-content;
  overflow: hidden;
}

.register-summary-details-title {
  color: #8e8e8e;
  font-family: "Barlow", sans-serif;
  font-size: 1.8vh;
  font-style: normal;
  font-weight: 400;
}

.register-summary-details-image {
  max-width: 18vh;
  max-height: 18vh;
  width: auto;
  height: auto;
}

.register-verifyOtp-container {
  justify-content: space-evenly;
}

.register-verifyOtp-text-error {
  color: red;
  font-family: "Barlow", sans-serif;
  font-size: 1.8vh;
  font-style: normal;
  font-weight: 400;
}

.register-verifyOtp-text-spam {
  color: #222222;
  font-family: "Barlow", sans-serif;
  font-size: 1.8vh;
  font-style: normal;
  font-weight: 400;
}

.register-verifyOtp-resend-button {
  background-color: rgba(0, 0, 0, 0);
  border-color: rgba(0, 0, 0, 0);
  color: #0055b8;
  font-family: "Barlow", sans-serif;
  font-size: 2.2vh;
  font-style: normal;
  font-weight: 400;
  text-decoration: underline;
  white-space: nowrap;
  width: auto;
}

.register-verifyOtp-resend-button:hover {
  background-color: rgba(0, 0, 0, 0);
  border-color: rgba(0, 0, 0, 0);
  color: #0055b8;
}

.register-verifyOtp-resend-button:focus {
  background-color: rgba(0, 0, 0, 0);
  border-color: rgba(0, 0, 0, 0);
  color: #0055b8;
}

.register-verifyOtp-resend-button:disabled {
  background-color: rgba(0, 0, 0, 0);
  border-color: rgba(0, 0, 0, 0);
  color: grey;
}

.register-palm-image {
  width: 180px;
  height: auto;
  margin-bottom: 10px;
}

.register-palm-otp-box {
  font-size: 32px;
  font-weight: bold;
  background-color: #f0f0f0;
  border-radius: 10px;
  padding: 16px 32px;
}

.register-palm-instruction-text {
  font-size: 16px;
  max-width: 400px;
}

.register-palm-otp-label {
  font-size: 18px;
  font-weight: bold;
}

.register-summary-status-image {
  width: 120px;
  height: auto;
}

.register-summary-status-text {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Mobile styles */

@media screen and (max-width: 480px) {
  /* Default styles */

  /* Standard styles */

  /* Layout styles */

  /* Page styles */
}

/* Layout */

.register-login-background {
  background-color: white;
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;

  position: relative;
  padding: 4vh 5vw;
}

.register-login-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  width: 100%;
}

/* Logo */
.register-login-header-logo {
  width: 30vw;
  max-width: 160px;
  height: auto;
  display: block;
  margin: 0 auto;
}

/* Title */
.register-login-title {
  background: linear-gradient(94.35deg, #0055b8 -1.49%, #9b5de5 111.73%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;

  display: inline-block;
  text-align: center;

  font-family: "Barlow", sans-serif;
  font-size: 3.5vh;
  font-weight: 700;
  font-style: normal;
  margin: 2rem 0 1rem;
}

/* Button */
.register-login-button {
  background: linear-gradient(94.35deg, #0055b8 -1.49%, #9b5de5 111.73%);
  color: white;
  font-family: "Barlow", sans-serif;
  font-size: 2.3vh;
  font-weight: 600;
  font-style: normal;

  display: flex; /* NEW: enables flex centering */
  align-items: center; /* NEW: vertical centering */
  justify-content: center; /* optional: horizontal centering */

  border: none;
  border-radius: 9999px;
  width: 100%;
  max-width: 480px;

  padding-top: 0.9rem; /* fine-tuned */
  padding-bottom: 0.9rem;

  line-height: 1; /* tighter text box */
  text-align: center;
  margin-bottom: 3rem;
  transition: all 0.3s ease;
}

.register-login-button:hover {
  opacity: 0.9;
  cursor: pointer;
  transform: scale(1.02);
}

/* Footer */
.register-login-footer-container {
  position: relative;
  width: 100%;
  background-color: #ebf4ff;
  padding: 0.75rem;
  text-align: center;
  margin-top: auto;
}

.register-login-footer-text {
  font-size: 1.4vh;
  font-family: "Barlow", sans-serif;
  color: black;
  margin: 0;
}

/* Detail */

/*
.register-detail-container {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 2rem 1rem;
}
*/

/*
.register-detail-background {
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4vh 2vw;
	box-sizing: border-box;
}
*/

.register-detail-form-input {
  width: 100%;
  padding: 1rem;
  font-size: 2vh;

  color: #222222;
  font-family: "Barlow", sans-serif;
  border: 1px solid #8e8e8e;
  border-radius: 14px;

  background-color: white;
  box-sizing: border-box;
  margin-top: 1vh;
  margin-bottom: 1vh;
}

.register-detail-form-input:focus {
  border: 2px solid #0055b8;
  box-shadow: none;
}

.register-detail-button-submit {
  background: linear-gradient(94.35deg, #0055b8 -1.49%, #9b5de5 111.73%);
  color: white;
  font-family: "Barlow", sans-serif;
  font-size: 2.3vh;
  font-weight: 600;
  font-style: normal;
  line-height: 1.2;
  text-align: center;

  margin-top: 2rem;

  display: flex; /* NEW: enables flex centering */
  align-items: center; /* NEW: vertical centering */
  justify-content: center; /* optional: horizontal centering */

  border: none;
  border-radius: 9999px;
  width: 100%;
  max-width: 100%;

  padding-top: 0.9rem; /* fine-tuned */
  padding-bottom: 0.9rem;

  line-height: 1; /* tighter text box */
  text-align: center;
  margin-bottom: 3rem;
  transition: all 0.3s ease;
}

.register-detail-button-submit:hover {
  cursor: pointer;
  opacity: 0.9;
  transform: scale(1.02);
}

.register-detail-wrapper {
  width: 100%;
  padding: 0 1rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

/*
.register-detail-form-stack {
	width: 100%;
	max-width: 480px;
}
*/

/*
.register-detail-step-text {
	text-align: center;
	color: #222;
	font-family: "Barlow", sans-serif;
	font-size: 2vh;
	font-weight: 500;
	margin-bottom: 0.5rem;
}
*/

.register-detail-title {
  background: linear-gradient(94.35deg, #0055b8 -1.49%, #9b5de5 111.73%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;

  font-family: "Barlow", sans-serif;
  font-weight: 700;
  font-size: 3vh;
  font-style: normal;
  text-align: left;
  margin-top: 2.5vh;
  margin-bottom: 2.5vh;
  white-space: nowrap;
}

/*
.register-step-label {
    font-size: 2vh;
    font-family: "Barlow", sans-serif;
    color: #000;
    margin-bottom: 0.5rem;
    display: block;
    text-align: left;
}
*/

/*
.register-detail-main-container {
    padding-top: 4vh;
    padding-bottom: 4vh;
    width: 100%;
    max-width: 480px;
}
*/

/* * {
  outline: 1px solid red;
} */
