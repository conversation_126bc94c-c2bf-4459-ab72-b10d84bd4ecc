////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		account.jsx
*	\brief		Account page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	26 Jun'24 : daryltan - aded auto logout after session expired or browser closed
*	25 Jun'24 : daryltan - added page
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import Cookies from 'js-cookie'
import { useEffect } from 'react'
import { Navigate , Outlet , useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify';

//Internal imports
import { useCustomerStore } from '../customer_store';

export default function Account() 
{
	/* Variables */

	//Hooks
	const m_navigate = useNavigate()

	//States
	const m_login_session_age = Cookies.get( 'login_session_age' )

	//State hooks
	const { session_id } = useCustomerStore()

	/* Functions */

	useEffect
	(	() =>
		{
			//Set timeout for auto logout
			const temp_timer = setTimeout
			(	() =>
				{
					//Set toast message
					toast.info
					(	'Your session has expired. Please login again to perform any account management.' 
					,	{	progress: undefined
						}
					)

					//Clear cookie
					Cookies.remove( 'login_session_age' )

					//Clear timer
					clearTimeout( temp_timer ) 

					//Navigate back to main
					m_navigate( '/' , { replace: true } )
				}
			,	m_login_session_age * 1000
			)
		}
	)
	
	if ( 0 === String( session_id ).length || undefined === m_login_session_age ) return <Navigate to='/' replace />
	return <Outlet />
}