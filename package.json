{"name": "palmpay-customer-management-app", "version": "0.1.0", "private": true, "dependencies": {"@stripe/react-stripe-js": "^3.8.0", "@stripe/stripe-js": "^7.6.1", "axios": "^1.11.0", "bootstrap": "^5.3.7", "js-cookie": "^3.0.5", "openid-client": "^6.6.2", "rc-progress": "^4.0.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.7.0", "react-spinners": "^0.17.0", "react-toastify": "^11.0.5", "react-tsparticles": "^2.12.2", "tsparticles": "^3.8.1", "web-vitals": "^2.1.4", "zustand": "^5.0.6"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "prettier": "^2.8.8", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "audit": "npm audit --omit=dev", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}