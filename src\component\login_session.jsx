////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		login_session.jsx
*	\brief		Login session page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	25 Jun'24 : daryltan - added removal of cookie upon logout
*	14 Jun'24 : daryltan - added edit payment page handling
*	20 Jun'24 : daryltan - added page
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import Cookies from 'js-cookie'
import { useState } from 'react'
import { Button , Col , Container , Image , Modal , Row , Stack } from 'react-bootstrap'
import { BiExit } from 'react-icons/bi';
import { useNavigate } from 'react-router-dom'

//Internal imports
import * as config from '../config'
import { useCustomerStore } from '../customer_store'
import { ParseFrontendAccountUpdateRoute } from '../data/route'


//Design imports
import '../style.css'

export default function LoginSession()
{
	/* Variables */

	//Hooks
	const m_navigate = useNavigate()

	//States
	const [m_alert , set_alert] = useState( false )

	//State hooks
	const { user_image , user_name_first , user_name_last , resetStore , session_id } = useCustomerStore()

	/* Functions */

	function HandleAgreeButton()
	{
		//Clear customer info
		resetStore()

		//Clear cookies
		Cookies.remove( 'login_session_age' )
			
		//Close alert
		set_alert( previous => false )

		//Navigate to root
		m_navigate( '/' , { replace: true } )
	}

	function HandleExitButton()
	{
		//Display alert
		set_alert( previous => true )
	}

	function HandleEditContact()
	{
		//Navigate to next page
		m_navigate( ParseFrontendAccountUpdateRoute( config.frontend.route.account.contact , session_id ) )
	}

	function HandleEditPayment()
	{
		//Navigate to next page
		m_navigate( ParseFrontendAccountUpdateRoute( config.frontend.route.account.payment , session_id ) )
	}

	return (
		<>
			<Container className="login-session-header-container">
				<Row className="pt-4">
					<Col className="align-items-center d-flex justify-content-center">
						<label className="login-session-header-container-text">Welcome Back!</label>
					</Col>
					<Col className="align-items-center d-flex justify-content-end">
						<Button className="login-session-exit-button" onClick={HandleExitButton}>
							<BiExit color="white" size={32} />
						</Button>
					</Col>
				</Row>
			</Container>

			<Stack className="pt-3 px-4" direction="vertical" gap={3}>
				<Container>
					<Row>
						<Col className="align-items-center d-flex justify-content-center">
							<Image className="login-session-body-user-image" rounded src={user_image} />
						</Col>
						<Col className="align-items-center d-flex justify-content-start">
							<label className="login-session-body-user-name">{user_name_first} {user_name_last}</label>
						</Col>
					</Row>
				</Container>

				<Container className="px-0">
					<Row>
						<label className="login-session-body-account-header">Account Settings</label>
					</Row>
					<Row>
						<Button className="login-session-body-edit-button" onClick={HandleEditContact}>
							<Row>
								<Col className="align-items-center d-flex justify-content-start">
									<label className="text-start">Edit mobile number</label>
								</Col>
								<Col className="align-items-center d-flex justify-content-end">
									<label className="text-end">{'>'}</label>
								</Col>
							</Row>							
						</Button>
						<hr className="m-0" />
					</Row>
					<Row>
						<Button className="login-session-body-edit-button" onClick={HandleEditPayment}>
							<Row>
								<Col className="align-items-center d-flex justify-content-start" xs='auto'>
									<label className="text-start">Edit payment information</label>
								</Col>
								<Col className="align-items-center d-flex justify-content-end">
									<label className="text-end">{'>'}</label>
								</Col>
							</Row>							
						</Button>
						<hr className="m-0" />
					</Row>
				</Container>
			</Stack>
			
			<Modal centered onHide={() => set_alert( previous => false )} show={m_alert}>
				<Modal.Header closeButton>
					<Modal.Title>Warning</Modal.Title>
				</Modal.Header>
				<Modal.Body>
					<p>You are currently logined to your account, you will need to login again to perform any changes to your account.</p>
					<p>Click Agree to go back to homepage.</p> 
					<p>Click Disagree to continue with your login session.</p>
				</Modal.Body>
				<Modal.Footer>
					<Container>
						<Row>
							<Col className="align-items-center d-flex justify-content-center">
								<Button onClick={HandleAgreeButton}>Agree</Button>
							</Col>
							<Col className="align-items-center d-flex justify-content-center">
								<Button onClick={() => set_alert( previous => false )}>Disagree</Button>
							</Col>
						</Row>
					</Container>
				</Modal.Footer>
			</Modal>
		</>
	)
}