////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		account_update_payment.jsx
*	\brief		Account update payment page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	21 Jun'24 : darytltan - added function
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import { Elements } from '@stripe/react-stripe-js'
import { loadStripe } from '@stripe/stripe-js'

//Internal imports
import { useCustomerStore } from "../customer_store";
import AccountUpdatePaymentStripe from './account_update_payment_stripe'

export default function AccountUpdatePayment()
{
	/* Variables */

	//Hooks
	const { stripe_secret } = useCustomerStore()

	//Stripe
	const m_stripe = loadStripe( process.env.REACT_APP_STRIPE_PUBLIC_KEY , { stripeAccount : process.env.REACT_APP_STRIPE_MERCHANT_ID } )
	const m_stripe_options = { clientSecret : stripe_secret }

	/* Functions */

	return (
		<>
			<Elements options={m_stripe_options} stripe={m_stripe}>
				<AccountUpdatePaymentStripe />
			</Elements>
		</>
	)
}