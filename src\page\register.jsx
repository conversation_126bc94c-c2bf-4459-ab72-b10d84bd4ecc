//Library imports
import { Line } from "rc-progress"
import { useEffect , useState } from "react";
import { <PERSON><PERSON> , Col , Container , Image , Modal , Row } from "react-bootstrap"
import { BiExit } from "react-icons/bi";
import { Navigate , Outlet , useLocation , useNavigate } from "react-router-dom";

//Internal imports
import { FrontendRoute } from "../data/route";
import { useCustomerStore } from "../customer_store";

//Asset imports
import register_summary_complete from '../asset/image/register_summary_complete.png'

export default function Register() 
{
    /* Variables */

    //Constants
    const m_steps = 3

    //Hooks
    //Track current page and enable navigation
    const m_location = useLocation()
    const m_navigate = useNavigate()

    //States
    //Track modal open/close
    const [m_alert , set_alert] = useState(false)

    //Track which step currently on baed on path
    const [m_currentPath , set_currentPath] = useState('')

    //Clear all customer values on exit
    const { resetStore, user_number_mobile } = useCustomerStore()

    /* Functions */
    //Track current URL path to determine which step to display
    useEffect
    (   () => 
        {
            //Set current path for tracking
            set_currentPath(m_location.pathname)
        }
    ,   [m_location]
    )

    function HandleAgreeButton() 
    {
        //Clear all store values
        resetStore()

        //Close alert
        set_alert(false)

        //Navigate to root
        m_navigate('/', { replace: true })
    }

    function HandleExitButton() 
    {
        //Display alert
        set_alert(previous => true)
    }

    //Main function to determine what step to render
    function RenderRegisterStep() 
    {
        //Switch case to handle step for different paths
        //Checks the current URL path
        switch (m_currentPath) 
        {
            //On the personal details form
            case FrontendRoute.register.detail:
                return RenderRegisterStepContent(1)

            //On the credit card details form
            case FrontendRoute.register.payment:
                return RenderRegisterStepContent(2)

            //On palm scanning
            case FrontendRoute.register.palm:
                return RenderRegisterStepContent(3)

            //On summary screen
            case FrontendRoute.register.summary:
                return <RenderRegisterSummary />

            //Default Case
            default:
                return <></>
        }
    }

    //Render step banner: Step 1/2/3 + Exit Button + Progress Line
    function RenderRegisterStepContent(step) 
    {
        return (
            <Container fluid className="register-step-header d-flex justify-content-between align-items-center px-4 py-3">
                <label className="register-step-text m-0">Step {step} of {m_steps}</label>
                    <Button className="register-header-exit-button" onClick={HandleExitButton}>
                        <BiExit color="black" size={28} />
                    </Button>
            </Container>
        )
    }
    
    //Render the progress bar fill (based on path)
    function RenderRegisterStepIndicator(step) 
    {
        //Determine line width to render for desktop/mobile view
        let temp_line_width = window.matchMedia("(max-width: 480px)").matches ? 1.5 : 0.8

        //Switch case to handle step for different paths
        switch (m_currentPath) 
        {
            case FrontendRoute.register.detail:
                return RenderRegisterStepIndicatorLine(33, temp_line_width)
      
            case FrontendRoute.register.payment:
                return RenderRegisterStepIndicatorLine(66, temp_line_width)
      
            case FrontendRoute.register.palm:
                return RenderRegisterStepIndicatorLine(100, temp_line_width)
      
            default:
                return <></>
        }
    }

    //Render progress bar line
    function RenderRegisterStepIndicatorLine(percent, line_width = 1) 
    {
        return (
            <Line
                percent={percent}
                strokeColor="#0055B8"
                strokeLinecap="square"
                strokeWidth={line_width}
                trailColor="rgba(0, 85, 184, 0.2)"
                trailWidth={line_width}
            />
        )
    }

    //Render success screen once scan is complete
    function RenderRegisterSummary() 
    {
        return (
            <>
                <Container className="register-summary-header-container d-flex flex-column m-0 p-0">
                    <Image className="register-summary-header-container-image" src={register_summary_complete} />
                </Container>
            </>
        )
    }

    //Hold until current path is known before activating
    if (m_currentPath && !user_number_mobile && m_currentPath !== FrontendRoute.register.detail) 
    { 
        return <Navigate to='/' replace />
    }

    /* Main UI */
    return (
        <>
            <RenderRegisterStep />
            <RenderRegisterStepIndicator />
            {/* Horizontally center children */}
            <div className="w-100">
                <Outlet />
            </div>

            <Modal centered onHide={() => set_alert( previous => false )} show={m_alert}>
			    <Modal.Header closeButton>
			        <Modal.Title>Warning</Modal.Title>
				</Modal.Header>
				<Modal.Body>
					<p>You are currently in the midst of registration, your details will not be saved if you return to homepage.</p>
					<p>Click Agree to go back to homepage.</p> 
					<p>Click Disagree to continue with registration.</p>
				</Modal.Body>
				<Modal.Footer>
					<Container>
						<Row>
							<Col className="align-items-center d-flex justify-content-center">
								<Button onClick={HandleAgreeButton}>Agree</Button>
							</Col>
							<Col className="align-items-center d-flex justify-content-center">
								<Button onClick={() => set_alert( previous => false )}>Disagree</Button>
							</Col>
						</Row>
					</Container>
				</Modal.Footer>
			</Modal>
        </>
    )
}