//Library imports
import axios from "axios";

// Internal imports
import { BackendRoute } from "../data/route";

//Create reusable axios client
const client = axios.create
(   {   baseURL: process.env.REACT_APP_BACKEND_API_URL
    ,   headers: { 'x-api-key': process.env.REACT_APP_BACKEND_API_KEY }
    ,   transformRequest: 
        [   (data, headers) => 
            {
                // Add merchant ID to every request
                data = { merchant_id: process.env.REACT_APP_STRIPE_MERCHANT_ID, ...data };

                // Convert to JSON
                return JSON.stringify(data);
            }
        ]
    }
)

//Send personal details to create Stripe customer
export const RegisterDetail = (data) => 
{
    return client.post(BackendRoute.customer.register.detail, data);
};

//Send OTP generation request for palm scan
export const RegisterOtpGenerate = (data) => 
{
    return client.post(BackendRoute.customer.register.otp, data);
};

//Send credit card info and palm biometric data to complete registration
export const RegisterComplete = (data) => 
{
    return client.post(BackendRoute.customer.register.complete, data);
};

//Send result request and receive scan result
export const RegisterPalmScanResult = (data) => 
{
  return client.post(BackendRoute.customer.register.result, data)
};