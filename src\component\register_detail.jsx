//Library imports
import { useState } from "react";
import {
  <PERSON><PERSON>,
  Container,
  Form,
  Modal,
  Col,
  Row,
  Stack,
} from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";

// Internal Imports
import { useCustomerStore } from "../customer_store";
import * as api from "../api/backend";
import { FrontendRoute } from "../data/route";
import RegisterLoading from "./register_loading";

//Design imports
import "../style.css";

export default function RegisterDetail() {
  /* Variables */

  //Hooks
  const m_navigate = useNavigate();

  //States (global store)
  const [m_alert, set_alert] = useState(false);
  const [m_loading, set_loading] = useState(false);

  //State hooks
  const {
    user_email,
    user_name_first,
    user_name_last,
    user_number_mobile,
    user_nric,
    setUserEmail,
    setUserNameFirst,
    setUserNameLast,
    setUserNumberMobile,
    setStripeCustomerId,
    setStripeSecret,
  } = useCustomerStore();

  /* Functions */

  //Capitalize the first letter of every name
  function CapitaliseName(event) {
    //Split string
    let temp_name_split = String(event.target.value).split(" ");

    //Check length of array
    if (temp_name_split.length === 1) {
      //Capitalised first name
      event.target.value =
        String(event.target.value).charAt(0).toUpperCase() +
        String(event.target.value).slice(1);
    } else {
      //Capitalised each name
      temp_name_split = temp_name_split.map((name) => {
        //Captalise first letter of each name
        return name.charAt(0).toUpperCase() + name.slice(1);
      });

      //Assign value
      event.target.value = temp_name_split.join(" ");
    }
  }

  //Allow only letters (A–Z) and spaces
  function ValidateNric(event) {
    //Check for valid keypress
    if (
      [
        "ArrowUp",
        "ArrowDown",
        "ArrowLeft",
        "ArrowRight",
        "Backspace",
        "Delete",
        "Tab",
      ].includes(event.key)
    ) {
      //Return for valid keypress
      return;
    } else {
      //Validate regex
      let temp_input = String(event.target.value + event.data);

      //Validate first char
      //note: check exceeding to handle when user selects all and inputs a new character
      if (1 === temp_input.length || 9 < temp_input.length)
        !/[S|F|M|T|G]/.test(event.data) && event.preventDefault();

      //Validate 7 numbers
      if (1 < temp_input.length && 9 >= temp_input.length)
        !/[0-9]/.test(event.data) && event.preventDefault();

      //Validate last char
      if (8 < temp_input.length && 9 >= temp_input.length)
        !/[A-Z]/.test(event.data) && event.preventDefault();
    }
  }

  //Allow only letters (A–Z) and spaces
  function ValidateName(event) {
    //Check for valid keypress
    if (
      [
        "ArrowUp",
        "ArrowDown",
        "ArrowLeft",
        "ArrowRight",
        "Backspace",
        "Delete",
        "Tab",
      ].includes(event.key)
    ) {
      //Return for valid keypress
      return;
    } else {
      //Validate regex
      !/[A-Za-z\s]/.test(event.data) && event.preventDefault();
    }
  }

  //Allow only 8-digit numbers starting with 8 or 9
  function ValidateNumberMobile(event) {
    //Check for valid keypress
    if (
      [
        "ArrowUp",
        "ArrowDown",
        "ArrowLeft",
        "ArrowRight",
        "Backspace",
        "Delete",
        "Tab",
      ].includes(event.key)
    ) {
      //Return for valid keypress
      return;
    } else {
      //Combine current value with latest value
      let temp_input = String(event.target.value + event.data);

      //Validate first number
      //note: check exceeding to handle when user selects all and inputs a new character
      if (1 === temp_input.length || 8 < temp_input.length)
        !/[8-9]/.test(event.data) && event.preventDefault();

      //Validate remaining 7 numbers
      if (1 < temp_input.length && 8 >= temp_input.length)
        !/[0-9]/.test(event.data) && event.preventDefault();
    }
  }

  /* Form Submission */

  function SubmitDetailForm(event) {
    //Prevent page refresh
    event.preventDefault();

    //Set loading
    set_loading(true);

    //Extract customer details
    const temp_email = event.target.email.value;
    const temp_name_first = event.target.name_first.value;
    const temp_name_last = event.target.name_last.value;
    const temp_number_mobile = event.target.number_mobile.value;

    //Store user details
    setUserEmail(temp_email);
    setUserNameFirst(temp_name_first);
    setUserNameLast(temp_name_last);
    setUserNumberMobile(temp_number_mobile);

    //-----Placeholder
    setTimeout(() => {
      setStripeCustomerId("mock_customer_id_123");
      setStripeSecret("mock_client_secret_xyz");
      set_loading(false);
      m_navigate(FrontendRoute.register.payment);
    }, 1000);
    //-----Placeholder

    /* -----Actual
        //Send to backend
        //Call backend API to create a Stripe customer
        api
            .RegisterDetail({
                number_mobile : temp_number_mobile,
                name_first : temp_name_first,
                name_last : temp_name_last, 
                email : temp_email
            }
        )    
            //Handle the response
            .then((response) =>
            {	
                //Check if response is successful
                if (response.data.success === true) 
                {
                    //Store Stripe info
                    setStripeCustomerId(response.data.data.stripe.customer_id)
                    setStripeSecret(response.data.data.stripe.secret)
                    
                    //Navigate to next page (for credit card details)
                    m_navigate(FrontendRoute.register.payment)
                } 
                else 
                {
                    //Set loading
                    set_loading(false)

                    //Set alert
                    set_alert(true)
                }
            }
        )
            .catch
            (   (error) => 
                {
                    //Log
                    console.log("Error", error)

                    //Set loading
                    set_loading(false)
                }
            )
        Actual----- */
  }

  /* Main UI */

  if (m_loading) return <RegisterLoading />;

  return (
    <>
      {m_loading && <RegisterLoading />}

      {!m_loading && (
        <Container fluid className="d-flex justify-content-center">
          <div className="register-detail-wrapper">
            <Form
              onSubmit={SubmitDetailForm}
              className="register-detail-main-container"
            >
              {/* Title */}
              <label className="register-detail-title d-block text-center">
                How do we address you?
              </label>

              {/* NRIC */}
              <Form.Group className="mt-4 mb-4">
                <Form.Control
                  className="register-detail-form-input"
                  defaultValue={user_nric}
                  name="nric"
                  onBeforeInput={ValidateNric}
                  pattern="[A-Z]{1}[0-9]{7}[A-Z]{1}"
                  placeholder="NRIC"
                  required
                />
              </Form.Group>

              {/* First Name */}
              <Form.Group className="mt-4 mb-4">
                <Form.Control
                  className="register-detail-form-input"
                  defaultValue={user_name_first}
                  name="name_first"
                  onBeforeInput={ValidateName}
                  onKeyUp={CapitaliseName}
                  pattern="^[A-Za-z\\s]{2,}"
                  placeholder="First Name"
                  required
                />
              </Form.Group>

              {/* Last Name */}
              <Form.Group className="mt-4 mb-4">
                <Form.Control
                  className="register-detail-form-input"
                  defaultValue={user_name_last}
                  name="name_last"
                  onBeforeInput={ValidateName}
                  onKeyUp={CapitaliseName}
                  pattern="^[A-Za-z\\s]{2,}"
                  placeholder="Last Name"
                  required
                />
              </Form.Group>

              {/* Mobile Number */}
              <Form.Group className="mt-4 mb-4">
                <Form.Control
                  className="register-detail-form-input"
                  defaultValue={user_number_mobile}
                  inputMode="numeric"
                  maxLength={8}
                  name="number_mobile"
                  onBeforeInput={ValidateNumberMobile}
                  pattern="[8-9]{1}[0-9]{7}"
                  placeholder="Mobile Number"
                  required
                />
              </Form.Group>

              {/* Email */}
              <Form.Group className="mt-4 mb-4">
                <Form.Control
                  className="register-detail-form-input"
                  defaultValue={user_email}
                  name="email"
                  type="email"
                  placeholder="Email"
                  required
                />
              </Form.Group>

              {/* Checkboxes */}
              <div className="mb-4">
                <Form.Check
                  className="mb-2"
                  id="register-detail-consent-checkbox-1"
                >
                  <Form.Check.Input required type="checkbox" />
                  <Form.Check.Label>
                    I confirm that the above details are correct.
                  </Form.Check.Label>
                </Form.Check>
                <Form.Check id="register-detail-consent-checkbox-2">
                  <Form.Check.Input required type="checkbox" />
                  <Form.Check.Label>
                    I acknowledge and agree to the{" "}
                    <Link
                      to={FrontendRoute.legal}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Terms & Conditions and Terms of Use
                    </Link>
                  </Form.Check.Label>
                </Form.Check>
              </div>

              {/* Submit Button */}
              <div className="d-flex justify-content-center w-100 mt-4">
                <Button type="submit" className="register-detail-button-submit">
                  Continue
                </Button>
              </div>
            </Form>
          </div>
        </Container>
      )}
    </>
  );
}
