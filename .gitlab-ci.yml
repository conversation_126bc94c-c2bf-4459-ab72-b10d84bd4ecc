include:
  - project: "wog/jtc/jtc-sdd/jtc-robotics/cicdbase"
    ref: "v1.3.1"
    file: 
      - "js/SCAN.gitlab-ci.yml"

# variables:
  # WORKING_DIR: "./" # OPTIONAL. Set to your working directory. Default="./"

sonarqube-job:
  # variables:
    # SONAR_URL: $SONAR_DEVELOPER_URL # Set to 'SONAR_COMMUNITY_URL' if using Community Edition
    # SONAR_TOKEN: $SONAR_DEVELOPER_TOKEN # Set to 'SONAR_COMMUNITY_TOKEN' if using Community Edition
    # SONAR_SOURCES: "./src" # Comma-seperated, source file locations
    # SONAR_EXCLUSIONS: "**/coverage/**/*.*" # Excluded files and paths
    # SONAR_COVERAGE: "./coverage/lcov.info" # lcov report location
  extends: .sonarqube-scan
