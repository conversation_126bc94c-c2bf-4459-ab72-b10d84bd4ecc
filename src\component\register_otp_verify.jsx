////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		register_verify_otp.jsx
*	\brief		OTP verification page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	28 Jun'24 : daryltan - optimised API call to separate file
*	5 Jun'24 : daryltan - removed empty code block from resend OTP
*	3 Jun'24 : daryltan - optimised desktop/mobile view
*	14 May'24 : daryltan - changed implementation to onboard to merchant directly
*	19 Apr'24 : daryltan - removed render functions, added OTP verification, added loading page
*	5 Apr'24 : daryltan - added skeleton function
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import { useState } from 'react'
import { Button , Col , Container , Image , Row , Stack } from 'react-bootstrap'
import { PiWarningCircleFill } from "react-icons/pi";
import { useNavigate } from 'react-router-dom'
import OtpInput from 'react18-input-otp'

//Internal imports
import { useCustomerStore } from '../customer_store'
import * as api from '../api/backend'
import { FrontendRoute } from '../data/route'
import RegisterLoading from './register_loading'

//Asset imports
import otp_icon from '../asset/image/otp.png'

export default function RegisterOtpVerify()
{
	/* Variables */

	//Constants
	const m_otp_input = 6;
	const m_resend_timeout = 30000

	//Hooks
	const m_navigate = useNavigate()

	//States
	const [m_loading , set_loading] = useState( false )
	const [m_otp , set_otp] = useState( '' )
	const [m_otp_error , set_otp_error] = useState( false )
	const [m_resend_disable , set_resend_disable] = useState( false )

	//State hooks
	const { session_id , user_email } = useCustomerStore()

	/* Functions */
	
	function HandleOtpInput( event )  
	{
		//Set OTP
		set_otp( event )

		//Refresh error 
		if ( true === m_otp_error ) set_otp_error( previous => false )
	}

	function RenderOtpText()
	{
		//Return element
		return (
			<>
			<Container>
				<Row className='pt-4'>
					{	m_otp_error
					?	<Stack direction='horizontal' gap={1}>
							<PiWarningCircleFill style={{ color: 'red' }} />
							<label className='register-verifyOtp-text-error'>You have entered the wrong code. Please try again.</label>
						</Stack>
					:	<label className='register-verifyOtp-text-spam'>Please check your spam email.</label>
					}
				</Row>
			</Container>
			</>
		)
	}

	function ResendOtpCode()
	{
		//Disable resend button
		set_resend_disable( previous => true ) 
		
		//Enable resend button after timeout
		setTimeout( () => set_resend_disable( previous => false ) , m_resend_timeout )

		//Resend otp code
		api.RegisterOtpGenerate( { email : user_email , session_id : session_id } )
		.catch( error => console.log( error ) )
	}

	function SubmitOtpForm( event )
	{
		//Prevent page refresh
		event.preventDefault()
		
		//Prevent sending of OTP if legnth not met
		if ( m_otp_input !== m_otp.length ) return

		//Set loading
		set_loading( previous => true )
		
		//Send to backend for verification
		api.RegisterOtpVerify( { session_id : session_id , otp : m_otp } )
		.then
		(	(response) => 
			{
				//Set loading
				set_loading( previous => false )

				//Check if response is successful
				if ( true === response.data.success ) 
				{
					//Navigate to next page
					m_navigate( FrontendRoute.register.payment )
				}
				else
				{
					//Set OTP error display
					set_otp_error( previous => true )
				}//end if
			}
		)
		.catch
		(	(error) =>
			{
				//Log
				console.log( error )

				//Set loading
				set_loading( previous => false )
			}
		)
	}

	if ( m_loading ) return <RegisterLoading />
	return (
		<>
			<Container>
				<Row>
					<label className="header-text">OTP Verification</label>
				</Row>
				<Row>
					<label>Enter the 6-digit code we sent to your email</label>
				</Row>
				<Row className='pt-5'>
					<Col className='align-items-center d-flex justify-content-center'>
						<Image src={otp_icon} />
					</Col>
				</Row>
				<Row className='align-items-start d-flex justify-content-start'>
					<Button className='register-verifyOtp-resend-button' disabled={m_resend_disable} onClick={ResendOtpCode}>Resend code</Button>
				</Row>
			</Container>
			
			<Container className='px-3'>
				<OtpInput 
					containerStyle='register-verifyOtp-container'
					errorStyle={{ border: '2px solid #E74F3D', borderRadius: '8px', width: '2.8em', height: '2.8em' }}
					focusStyle={{ border: '2px solid #0055B8', boxShadow: 'none', outline: 'none' }}
					inputStyle={{ border: '1px solid #8E8E8E', borderRadius: '8px', width: '2.8em', height: '2.8em' }}
					hasErrored={m_otp_error}
					isInputNum
					numInputs={m_otp_input}
					onChange={HandleOtpInput}
					shouldAutoFocus
					value={m_otp}
				/> 
			</Container>

			<RenderOtpText />

			<Container className='register-container-submit-button'>
				<Row className='align-items-center d-flex justify-content-center'>
					<Button onClick={SubmitOtpForm}>Verify</Button>
				</Row>
			</Container>
		</>
	);
}