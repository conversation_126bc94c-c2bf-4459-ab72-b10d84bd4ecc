////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		datetime.js
*	\brief		Utility functions for datetime manipulation
*	\author		daryltan
*	\date		2024
*	\copyright	Developed by daryltan for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	25 Jun'24 : daryltan - added ParseDateTimeOffsetSeconds()
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Export helper functions
export const ParseDateTimeOffsetSeconds = ( offset_seconds ) => { return new Date( new Date().getTime() + offset_seconds * 1000 ) }