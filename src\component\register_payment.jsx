//Library imports

import { useEffect, useState } from 'react'
import { Container, Row } from 'react-bootstrap'
import { Elements } from '@stripe/react-stripe-js'
import { loadStripe } from '@stripe/stripe-js'

//Internal imports
import { useCustomerStore } from '../customer_store'
import RegisterLoading from "./register_loading"
import RegisterPaymentStripe from './register_payment_stripe'

export default function RegisterPayment() 
{
    /* Variables */

    //Hooks
    //Get Stripe client_secret from store
    const { stripe_secret } = useCustomerStore()

    //States
    //UI state to show loading indicator
    const [m_loading, set_loading] = useState(false)

    //-----Actual
    //Stripe
    //Load Stripe using .env key
    const m_stripe = loadStripe(process.env.REACT_APP_STRIPE_PUBLIC_KEY, { stripeAccount: process.env.REACT_APP_STRIPE_MERCHANT_ID })
    
    //Stripe UI
    const m_stripe_options = { clientSecret: stripe_secret }

    /* Functions */

    //Show loading screen if needed
    //if (m_loading) return <RegisterLoading />

    // return (
    //     <>
    //         <Container>
    //             <Row>
    //                 <label className="header-text">Payment Info</label>
    //             </Row>
    //         </Container>

    //         <Elements options={m_stripe_options} stripe={m_stripe}>
    //             <RegisterPaymentStripe setLoading={ ( value ) => set_loading( previous => value) } />
    //         </Elements>
    //     </>
    // )
    //-----Actual

    

    //-----Placeholder
    const mock_secret = "mock_secret_123"
    const actual_secret = stripe_secret?.startsWith("mock_") ? mock_secret : stripe_secret

    //-----Placeholder
    if (m_loading) return <RegisterLoading />

    return (
        <>
            <Container>
                <Row>
                    <label className="header-text">Payment Info</label>
                </Row>
            </Container>

            {stripe_secret?.startsWith("mock_") ? 
                (
	                //-----Placeholder UI (without Stripe)
	                <RegisterPaymentStripe setLoading={set_loading} />
                ) : (
	                //-----Actual Stripe UI
	                <Elements options={m_stripe_options} stripe={m_stripe}>
		                <RegisterPaymentStripe setLoading={set_loading} />
	                </Elements>
                )
            }
        </>
    )
    //-----Placeholder
}