//Library imports
import { <PERSON><PERSON>, <PERSON>, Container, Image, Row, Stack } from 'react-bootstrap'
import { useNavigate } from 'react-router-dom'

//Internal imports
import RegisterLoading from './register_loading'
import { useCustomerStore } from '../customer_store'
import { FrontendRoute } from '../data/route'

//Design imports
import '../style.css'

//Asset imports
import success_image from '../asset/image/status_success.png'
import failure_image from '../asset/image/status_fail.png'

export default function RegisterSummary() 
{
    /* Variables */

    //Hooks
    const m_navigate = useNavigate()

    //Store
    const { palm_scan_result, resetStore } = useCustomerStore()

    //Handler
    //Go back to home if failed
    function HandleTryAgain() 
    {
        resetStore()
        m_navigate('/', { replace: true })
    }

    /* Main UI */

    if (palm_scan_result === null) return <RegisterLoading />

    return (
    <>
        <Container className="py-2">
            <Row>
            <label className="header-text">Account Summary</label>
            </Row>
        </Container>

        <Container className="px-3">
            <Stack gap={4} className="align-items-center text-center">
                {/* Image */}
                <Image
                    src={palm_scan_result ? success_image : failure_image}
                    className="register-summary-status-image"
                    alt={palm_scan_result ? "Success" : "Failure"}
                />

                {/* Text */}
                <label className="register-summary-status-text">
                    {palm_scan_result
                    ? "All Set! You can now use palm payment at the counter."
                    : "Registration failed. Please try again by starting the registration process over."}
                </label>

                {/* Retry Button (Failure only) */}
                {!palm_scan_result && 
                    (
                        <Button onClick={HandleTryAgain}>Try Again</Button>
                    )
                }
            </Stack>
        </Container>
    </>
  )
}
