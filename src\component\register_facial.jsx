////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		register_facial.jsx
*	\brief		Facial registration page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	12 Jul'24 : daryltan - amended correct facial instructions, replaced confirm checkbox with disclaimer text, removed incorrect facial instructions, minor design amendments
*	10 Jul'24 : daryltan - changed image quality check first before confirmation
*	3 Jul'24 : daryltan - minor design amendments
*	28 Jun'24 : daryltan - optimised API call to separate file
*	5 Jun'24 : daryltan - minor design changes
*	3 Jun'24 : daryltan - optimised desktop/mobile view
*	28 May'24 : daryltan - minor design amendments
*	14 May'24 : daryltan - changed implementation to onboard to merchant directly, added loading page
*	9 Apr'24 : daryltan - added sending of photo to backend for validation checks and storing after response
*	4 Apr'24 : daryltan - added capture, instructions, summary screens
*	2 Apr'24 : daryltan - added skeleton function
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import { useState } from "react"
import { Button , Col , Container , Form , Image , Modal , Row , Stack } from "react-bootstrap"
import { PiWarningCircle } from "react-icons/pi"
import { useNavigate } from "react-router-dom"
import Webcam from "react-webcam"

//Internal imports
import * as config from '../config'
import { useCustomerStore } from '../customer_store'
import * as api from '../api/backend'
import { FrontendRoute } from '../data/route'
import RegisterLoading from './register_loading'

//Design imports
import "../style.css"

//Asset imports
import facial_example from '../asset/image/facial.png'
import facial_correct_1 from '../asset/image/facial_correct_1.png'
import facial_correct_2 from '../asset/image/facial_correct_2.png'
import facial_correct_3 from '../asset/image/facial_correct_3.png'
import facial_correct_4 from '../asset/image/facial_correct_4.png'
import facial_correct_icon from '../asset/image/facial_correct_icon.png'
import facial_incorrect_icon from '../asset/image/facial_incorrect_icon.png'

export default function RegisterFacial()
{
	/* Variables */

	//Hooks
	const m_navigate = useNavigate()

	//States
	const [m_camera_on , set_camera_on] = useState( false )
	const [m_guideline_toggle , set_guideline_toggle] = useState( false )
	const [m_image , set_image] = useState( facial_example )
	const [m_image_retake , set_image_retake] = useState( false )
	const [m_loading , set_loading] = useState( false )

	//State hooks
	const { session_id , setUserImage } = useCustomerStore()
	
	/* Functions */
	
	function HandleCameraCapture( image )
	{
		//Set loading 
		set_loading( previous => true )
		
		//Reset image retake flag
		set_image_retake( previous => false )

		//Set captured image
		set_image( previous => image )

		//Disable camera capture
		set_camera_on( previous => false )
		
		//Send to backend
		api.RegisterFacial( { session_id : session_id , image : image } )
		.then
		(	response =>
			{
				//Check if response is successful
				if ( true === response.data.success ) 
				{
					//Store image to data store
					setUserImage( image )

					//Set loading
					set_loading( previous => false )
				}
				else
				{
					//Go to retake page
					set_image_retake( previous => true )

					//Set loading
					set_loading( previous => false )
				}//end if
			}			
		)
		.catch
		(	error =>
			{
				//Log error
				console.log( error )

				//Set loading
				set_loading( previous => false )
			}
		)
	}

	function HandleCameraOpen()
	{
		//Set capture mode
		set_camera_on( previous => true )

		//Scroll to top of screen
		window.scrollTo( 0 , 0 )
	}

	function RenderFacialCamera()
	{
		//Return element
		return (
			<>
				<Container>
					<Row>
						<label className="header-text">Capture Photo</label>
					</Row>
					<Row className="align-items-center d-flex justify-content-center">
						<Webcam 
							className="register-facial-webcam p-2"
							disabled={!m_camera_on}
							imageSmoothing={true}
							minScreenshotWidth={config.frontend.camera.screenshot_width}
							minScreenshotHeight={config.frontend.camera.screenshot_height}
							mirrored={true}
							screenshotFormat="image/jpeg"
							screenshotQuality={1}
							videoConstraints={{ facingMode: "user" }}
						>
							{	({ getScreenshot }) => 
								(	
										<Button 
											className="register-facial-button-capture mt-3" 
											onClick={() => HandleCameraCapture( getScreenshot() ) }
										>
											Capture Photo
										</Button>
								)
							}
						</Webcam>
					</Row>
				</Container>
			</>
		)
	}
	
	function RenderFacialConfirm()
	{
		//Return element
		return (
			<>
				<Form onSubmit={SubmitFacialImage}>
					<Container className="px-3">
						<Row>
							<label className="header-text">Confirm Photo</label>
						</Row>
						<Row className="align-items-center d-flex justify-content-center">
							<Image className="register-facial-image p-0" src={m_image} />
						</Row>
						<Row>
							<Col className="align-items-center d-flex justify-content-center">
								<Button 
									className="register-facial-image-guideline-button align-items-center d-flex justify-content-center" 
									onClick={() => set_guideline_toggle( previous => true )}
								>
									<Stack direction="horizontal" gap={2}>
										<PiWarningCircle color="#0055B8" size={16}/> 
										<label className="register-facial-button-guidelines" style={{textDecoration: "underline"}}>View Photo Guidelines</label>
									</Stack>
								</Button>
							</Col>
						</Row>
						<Row>
							<Col className="align-items-center d-flex justify-content-center">
								<label className="register-facial-confirm-text">
									By tapping on "Looks Good" to proceed to the next step, <br />
									you confirm that you are the person captured in the photo.
								</label>
							</Col>
						</Row>
						<Row className="pt-3">
							<Stack className="align-items-center d-flex justify-content-center" gap={3}>
								<Button type="submit">Looks Good</Button>
								<Button className="register-facial-image-retake-button" onClick={() => HandleCameraOpen()}>Retake Photo</Button>
							</Stack>
						</Row>
					</Container>
				</Form>
			</>
		)
	}

	function RenderFacialGuideline()
	{
		//Return element
		return (
			<>
				<Modal centered onHide={() => set_guideline_toggle( previous => false )} show={m_guideline_toggle}>
					<Modal.Header closeButton style={{ border: 0 , margin: 0 }}></Modal.Header>
					<Modal.Body>
						<Stack gap={2}>
							<Stack className="pb-4" gap={1}>
								<label className="header-text">Guidelines</label>
								<label>Ensure your photo meets the guidelines.</label>
							</Stack>

							<RenderFacialInstructionCorrect />
						</Stack>
					</Modal.Body>
				</Modal>
			</>
		)
	}

	function RenderFacialInstruction() 
	{
		//Return element
		return (
			<>
				<Container className="pb-2">
					<Row>
						<label className="header-text">Guidelines</label>
					</Row>
					<Row>
						<label>Upload a selfie to activate your facial-recognition account.</label>
					</Row>
				</Container>

				<Container>
					<Row  xs={1} sm={1} md={2}>
						<Col className="align-items-center d-flex justify-content-center px-4">
							<RenderFacialInstructionCorrect />
						</Col>
						<Col className="align-items-center d-flex justify-content-center">
							<Image className="register-facial-image-example" src={m_image} />
						</Col>
					</Row>

					<Row className="pt-4">
						<Col className="align-items-center d-flex justify-content-center">
							<Button className="register-facial-button-capture" onClick={() => HandleCameraOpen()}>Take Photo</Button>
						</Col>
					</Row>
				</Container>
			</>
		)
	}
	
	function RenderFacialInstructionCorrect()
	{
		//Return element
		return (
			<Stack className="register-facial-instructions-correct my-3 px-3 py-4" gap={2}>
				<Image className="register-facial-instructions-icon" src={facial_correct_icon} />
				<Stack direction="horizontal" gap={2}>
					<Image src={facial_correct_1} />
					<label>Be in well-lit environment</label>
				</Stack>
				<Stack direction="horizontal" gap={2}>
					<Image src={facial_correct_2} />
					<label>Plain and white background</label>
				</Stack>
				<Stack direction="horizontal" gap={2}>
					<Image src={facial_correct_3} />
					<label>Facial features must be seen in full front view, showing your shoulders and hair</label>
				</Stack>
				<Stack direction="horizontal" gap={2}>
					<Image src={facial_correct_4} />
					<label>Keep a neutral expression with mouth closed</label>
				</Stack>
			</Stack>
		)
	}

	function RenderFacialRetake()
	{
		//Return element
		return (
			<>
				<Container className="px-3">
					<Row>
						<label className="header-text">Retake Photo</label>
					</Row>
					<Row className="align-items-center d-flex justify-content-center p-3">
						<Stack className="register-facial-retake-container p-0">
							<Image className="register-facial-retake-icon p-0" src={facial_incorrect_icon} />
							<Image className="register-facial-image p-0" src={m_image} />
						</Stack>
					</Row>
					<Row >
						<Col className="align-items-center d-flex justify-content-center">
							<Button 
								className="register-facial-image-guideline-button align-items-center d-flex justify-content-center" 
								onClick={() => set_guideline_toggle( previous => true )}
							>
								<Stack direction="horizontal" gap={2}>
									<PiWarningCircle color="#0055B8" size={16}/> 
									<label style={{textDecoration: "underline"}}>View Photo Guidelines</label>
								</Stack>
							</Button>
						</Col>
					</Row>
					<Row className="align-items-center d-flex justify-content-center pt-3">
						<Button onClick={() => HandleCameraOpen()}>Retake Photo</Button>
					</Row>
				</Container>
			</>
		)
	}
	
	function SubmitFacialImage( event )
	{
		//Prevent page refresh
		event.preventDefault()
		
		//Go to next page
		m_navigate( FrontendRoute.register.detail , { replace : true } )
	}

	if ( m_loading ) return <RegisterLoading />
	return (
		<>
			{	( m_camera_on )
			?	<RenderFacialCamera />
			:	( facial_example === m_image )
				?	<RenderFacialInstruction />
				:	( m_image_retake )
					?	<RenderFacialRetake />
					:	<RenderFacialConfirm />
			}

			<RenderFacialGuideline />
		</>
	);
}