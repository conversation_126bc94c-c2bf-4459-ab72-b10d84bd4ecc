////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		account_update_contact.jsx
*	\brief		Account update contact page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	28 Jun'24 : daryltan - optimised API call to separate file
*	24 Jun'24 : daryltan - minor element amendments
*	21 Jun'24 : darytltan - added function
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import { useState } from 'react';
import { Button , Col , Container , Form , Image , Row , Stack } from 'react-bootstrap'
import { IoIosArrowForward } from "react-icons/io";
import { useNavigate } from 'react-router-dom';
import { PulseLoader } from 'react-spinners'

//Internal imports
import { useCustomerStore } from '../customer_store';
import * as api from '../api/backend'
import { FrontendRoute } from '../data/route';
import AccountUpdate from './account_update';

//Asset imports
import status_fail from '../asset/image/status_fail.png'
import status_success from '../asset/image/status_success.png'

export default function AccountUpdateContact()
{
	/* Variables */

	//Hooks
	const m_navigate = useNavigate()

	//States
	const [m_edit , set_edit] = useState( false )
	const [m_loading , set_loading] = useState( false )
	const [m_status , set_status] = useState( false )
	const [m_complete , set_complete] = useState( false )

	//State hooks
	const { user_number_mobile , session_id , setUserNumberMobile } = useCustomerStore()
	
	/* Functions */
	
	function HandleCompleteButton()
	{
		//Navigate to account page
		m_navigate( FrontendRoute.account.session + '/' + String( session_id ) )
	}

	function HandleEditButton()
	{
		//Set edit mode
		set_edit( previous => !m_edit )
	}

	function RenderUpdateLoading()
	{
		//Return element
		return (
			<>
				<Container className='register-loading-container'>
					<Row className='pb-5'>
						<Col className='align-items-center d-flex justify-content-center'>
							<PulseLoader color='#0055B8' loading={true} size={25} speedMultiplier={0.8}/>
						</Col>
					</Row>
					<Row className='align-items-center d-flex justify-content-center'>
						<label className='register-loading-container-text'>Almost there...</label>
					</Row>
				</Container>
			</>
		)
	}

	function RenderUpdateForm()
	{
		//Return element
		return (
			<>
				<AccountUpdate />

				<Container className="px-4 pb-3">
					<Row>
						<label className="login-session-update-body-header-text text-start">Mobile Number</label>
					</Row>
				</Container>

				<Form onSubmit={SubmitContactForm}>
					<Container className="px-4">
						{	m_edit
						?	<Row className="align-items-center d-flex justify-content-start pb-2">
								<label className="login-session-update-body-input-header">Enter new number</label>
							</Row>
						:	<Row>
								<Col className="align-items-center d-flex justify-content-start">
									<label className="login-session-update-body-input-header">Your number</label>
								</Col>
								<Col className="align-items-center d-flex justify-content-end">
									<Button className="login-session-update-body-edit-button" onClick={HandleEditButton}>
										<Stack className="justify-content-center" direction="horizontal" gap={3}>
											<label>Edit</label>
											<IoIosArrowForward size={18} />
										</Stack>	
									</Button>
								</Col>
							</Row>
						}

						<Row className="px-2">
							<Form.Control 	
								className="login-session-update-body-input-field px-3"
								disabled={!m_edit}
								inputMode="numeric"
								maxLength={8} 
								name="number_mobile"
								onBeforeInput={(event) => ValidateNumberMobile(event)}
								pattern="[8-9]{1}[0-9]{7}"
								placeholder={m_edit ? '' : user_number_mobile} 
								required
								type="text"
							/>
						</Row>
					</Container>
				
					{	m_edit
					?	<Container className="login-session-update-body-button-position">
							<Row className="align-items-center d-flex justify-content-center">
								<Button type="submit">
									Save
								</Button>
							</Row>
						</Container>
					:	''
					}
				</Form>
			</>
		)
	}

	function RenderUpdateStatus()
	{
		//Return element
		return (
			<>
				<Container className="login-session-update-body-container-position px-3">
					<Row>						
						<Col className="align-items-center d-flex justify-content-center pb-3">
							{	m_status
							?	<Image src={status_success} />
							:	<Image src={status_fail} />
							}
						</Col>
					</Row>
					<Row className="pt-2">
						{	m_status
						?	<label className="login-session-update-body-container-subheader-text text-center">Updated!</label>
						:	<label className="login-session-update-body-container-subheader-text text-center">Failed to update!</label>
						}
					</Row>
					<Row className="pt-3 px-5">
						{	m_status
						?	<label className="login-session-update-body-container-subtext-text text-center">Your mobile number has been successfully updated.</label>
						:	<label className="login-session-update-body-container-subtext-text text-center">Your mobile number was not updated. Please try again.</label>
						}
					</Row>
				</Container>

				<Container className="login-session-update-body-button-position">
					<Row className="align-items-center d-flex justify-content-center">
						<Button onClick={HandleCompleteButton}>
							{	m_status
							?	'Got it!'
							:	'Retry'
							}
						</Button>
					</Row>
				</Container>
			</>
		)
	}

	function SubmitContactForm( event )
	{
		//Prevent page refresh
		event.preventDefault()

		//Set loader
		set_loading( previous => true )

		//Extract value
		const temp_number_mobile = event.target.number_mobile.value

		//Send to backend
		api.UpdateContact( { session_id : session_id , number_mobile : temp_number_mobile } )
		.then
		(	(response) =>
			{
				//Set loader
				set_loading( previous => false )

				//Set complete
				set_complete( previous => true )

				//Check if response is successful
				if ( true === response.data.success )
				{
					//Store mobile number
					setUserNumberMobile( temp_number_mobile )

					//Set status
					set_status( previous => true )
				}
				else
				{
					//Set status
					set_status( previous => false )
				}//end if
			}
		)
		.catch
		(	error =>
			{	
				//Log
				console.log( error )
				
				//Set loader
				set_loading( previous => false )

				//Set complete
				set_complete( previous => true )

				//Set status
				set_status( previous => false )
			}
		)
	}

	function ValidateNumberMobile( event )
	{
		//Check for valid keypress
		if	(	"ArrowUp" === event.key
			||	"ArrowDown" === event.key 
			||	"ArrowLeft" === event.key 
			||	"ArrowRight" === event.key 
			||	"Backspace" === event.key 
			||	"Delete" === event.key 
			||	"Tab" === event.key
			) 
		{ 
			//Return for valid keypress
			return 
		} 
		else 
		{ 
			//Combine current value with latest value
			let temp_input = String( event.target.value + event.data )
			
			//Validate first number 
			//note: check exceeding to handle when user selects all and inputs a new character
			if ( 1 === temp_input.length || 8 < temp_input.length ) !/[8-9]/.test( event.data ) && event.preventDefault() 

			//Validate remaining 7 numbers
			if ( 1 < temp_input.length && 8 >= temp_input.length ) !/[0-9]/.test( event.data ) && event.preventDefault()
		}//end if statement
	}
	
	if ( m_loading ) return <RenderUpdateLoading />
	return (
		<>
			{	!m_complete
			?	<RenderUpdateForm />
			:	<RenderUpdateStatus />
			}
		</>
	)
}