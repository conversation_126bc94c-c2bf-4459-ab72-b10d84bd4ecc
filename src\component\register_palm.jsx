//Library imports
import { useState, useEffect } from 'react'
import { Col, Container, Image, Row, Stack } from "react-bootstrap"
import { useNavigate } from 'react-router-dom'

//Internal imports
import { useCustomerStore } from '../customer_store'
import { FrontendRoute } from '../data/route'
import * as api from '../api/backend'

//Design imports
import '../style.css'

//Asset imports
import register_scan from '../asset/image/register_scan.png'

export default function RegisterPalm() {
	
    /* Variables */

	//Hooks
	const m_navigate = useNavigate()

	//Global store values
	const { stripe_customer_id, setPalmScanResult } = useCustomerStore()

	//States
	const [m_otp, set_otp] = useState('------')

	/* Functions */

    //-----Placeholder
	//Simulate fetch OTP from backend
	useEffect
    (   () => 
        {
		    //Simulate OTP generation
		    const temp_mock_otp = Math.floor(100000 + Math.random() * 900000)
		    set_otp(temp_mock_otp.toString())

		    //Simulate waiting for scan result
		    const timer = setTimeout(() => 
            {
                //Set result
			    setPalmScanResult(true) // Set false to test failure screen

			    //Navigate to summary screen
			    m_navigate(FrontendRoute.register.summary)
		    },  3000)
            
            return () => clearTimeout(timer)
	    }, 
        [
        ]
    )
    //-----Placeholder

	/* -----Actual
	useEffect
    (   () => 
        {
	        api.RegisterPalmScanResult
            (   {
                customer_id: stripe_customer_id
            ,   otp: m_otp 
                }
            )
		    .then
            (   (response) => 
                {
                    if (response.data.success === true) 
                    {
                        setPalmScanResult(true)
                        m_navigate(FrontendRoute.register.summary)
                    } 
                    else 
                    {
                        setPalmScanResult(false)
                        m_navigate(FrontendRoute.register.summary)
                    }
                }   
            )
			.catch
            (   (error) => 
                {
                    console.log("Error", error)
                    setPalmScanResult(false)
                    m_navigate(FrontendRoute.register.summary)
                }   
            )
	    }
        ,   
        [
        ]
    )
	-----Actual */

	/* Main UI */

	return (
        <>
            <Container>
                <Row>
                    <label className="header-text">Here's your code!</label>
                </Row>
		        <Row className='pt-5'>
                    <Col className='align-items-center d-flex justify-content-center'>
                        <Image src={register_scan} />
                    </Col>
                </Row>
				<Row>
				    <Col className="text-center">
					    <h2 style={{ fontWeight: 'bold', fontSize: '2.5rem' }}>{m_otp}</h2>
				    </Col>
			    </Row>
                <Row>
                    <label>Please use this code as your User ID and scan your palm on the scanner</label>
                </Row>
		    </Container>
        </>
	);
}