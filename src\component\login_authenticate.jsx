////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		login_facial.jsx
*	\brief		Login via facial authentication page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	12 Jul'24 : daryltan - amended correct facial instructions
*	10 Jul'24 : daryltan - changed login flow to authenticate after image capture
*	28 Jun'24 : daryltan - optimised API call to separate file
*	26 Jun'24 : daryltan - added exit button, changed cookie to store the session age
*	25 Jun'24 : daryltan - added creating and storing of cookie
*	21 Jun'24 : daryltan - added storing of customer setup secret
*	20 Jun'24 : daryltan - added getting and storing of customer/payment details via session
*	19 Jun'24 : daryltan - added facial capture and authentication
*	18 Jun'24 : daryltan - added skeleton page
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import Cookies from 'js-cookie'
import { useState } from "react"
import { Button , Col , Container , Image , Modal , Row , Stack } from "react-bootstrap"
import { PiWarningCircle } from "react-icons/pi"
import { useNavigate } from "react-router-dom"
import Webcam from "react-webcam"

//Internal imports
import * as config from '../config'
import { useCustomerStore } from '../customer_store'
import * as api from '../api/backend'
import { FrontendRoute } from '../data/route'
import { ParseDateTimeOffsetSeconds } from '../utility/datetime'
import LoginLoading from './login_loading'

//Design imports
import "../style.css"

//Asset imports
import facial_correct_icon from '../asset/image/facial_correct_icon.png'
import facial_correct_1 from '../asset/image/facial_correct_1.png'
import facial_correct_2 from '../asset/image/facial_correct_2.png'
import facial_correct_3 from '../asset/image/facial_correct_3.png'
import facial_correct_4 from '../asset/image/facial_correct_4.png'
import jtc from "../asset/image/jtc.png"
import status_fail from "../asset/image/status_fail.png"
import status_success from "../asset/image/status_success.png"

export default function LoginAuthenticate() 
{
	/* Variables */

	//Hooks
	const m_navigate = useNavigate()

	//States
	const [m_camera_on , set_camera_on] = useState( true )
	const [m_guideline_toggle , set_guideline_toggle] = useState( false )
	const [m_image_pass , set_image_pass] =  useState( false )
	const [m_image_retake , set_image_retake] = useState( false )
	const [m_loading_login , set_loading_login] = useState( false )

	//State hooks
	const { setSessionId , setUserEmail , setUserImage , setUserNameFirst , setUserNameLast , setUserNumberMobile , setStripePaymentBrand , setStripePaymentExpiry , setStripePaymentLast4 , setStripeSecret } = useCustomerStore()
	
	/* Functions */
	
	function HandleCameraCapture( image )
	{
		//Set loading 
		set_loading_login( previous => true )

		//Reset image retake flag
		set_image_retake( previous => false )

		//Disable camera capture
		set_camera_on( previous => false )
		
		//Post image
		api.LoginFacial( { image : image } )
		.then
		(	response => 
			{
				//Set loading
				set_loading_login( previous => false )

				//Check if response is successful
				if ( true === response.data.success ) 
				{
					//Set success page
					set_image_pass( previous => true )

					//Store session ID 
					let temp_session_id = response.data.data.session_id
					
					//Set session ID
					setSessionId( temp_session_id )
					
					//Get session details
					api.LoginSession( { session_id : response.data.data.session_id } )
					.then
					(	response =>
						{
							//Set login cookie
							Cookies.set( 'login_session_age' , response.data.data.age , { expires: ParseDateTimeOffsetSeconds( response.data.data.age ) } )

							//Store customer details
							setUserEmail( response.data.data.email )
							setUserImage( response.data.data.image )
							setUserNameFirst( response.data.data.name_first )
							setUserNameLast( response.data.data.name_last )
							setUserNumberMobile( response.data.data.number_mobile )

							//Store payment details
							setStripePaymentBrand( response.data.data.stripe_payment_brand )
							setStripePaymentExpiry( response.data.data.stripe_payment_expiry )
							setStripePaymentLast4( response.data.data.stripe_payment_last4 )
							setStripeSecret( response.data.data.stripe_secret )

							//Navigate to next page
							m_navigate( FrontendRoute.account.session + '/' + String( temp_session_id ) )
						}
					)
					.catch
					(	error =>
						{
							//Log error
							console.log( error )
						}
					)
				}
				else
				{
					//Go to retake page
					set_image_retake( previous => true )
				}//end if
			}
		)
		.catch 
		(	error =>
			{
				//Log error
				console.log( error )

				//Set loading
				set_loading_login( previous => false )
			}
		)
	}

	function HandleCameraOpen()
	{
		//Scroll to top of screen
		window.scrollTo( 0 , 0 )

		//Reset image retake flag
		set_image_retake( previous => false )

		//Disable camera capture
		set_camera_on( previous => true )
	}

	function RenderFacialCamera()
	{
		//Return element
		return (
			<>
				<Container className="m-0 p-0 register-login-background-image">
					<Container className="p-4 register-login-container-header">
						<Container>
							<Row>
								<Col className="align-items-center d-flex justify-content-center">
									<label className="register-login-header-text">Facial-Recognition Payment</label>
								</Col>
								<Col className="align-items-center d-flex justify-content-center">
									<Image className="register-login-header-logo" src={jtc} />
								</Col>
							</Row>
						</Container>

						<Container>
							<Row className="align-items-center d-flex justify-content-center">
								<Webcam 
									className="register-facial-webcam p-2"
									disabled={!m_camera_on}
									imageSmoothing={true}
									minScreenshotWidth={config.frontend.camera.screenshot_width}
									minScreenshotHeight={config.frontend.camera.screenshot_height}
									mirrored={true}
									screenshotFormat="image/jpeg"
									screenshotQuality={1}
									videoConstraints={{ facingMode: "user" }}
								>
									{	({ getScreenshot }) => 
										(	
											<Row className="align-items-center d-flex justify-content-center">
												<Button 
													className="register-facial-button-capture mt-3" 
													onClick={() => HandleCameraCapture( getScreenshot() ) }
												>
													Capture Photo
												</Button>
											</Row>
										)
									}
								</Webcam>
							</Row>
							<Row>
								<Col className="align-items-center d-flex justify-content-center mt-3">
									<Button className="register-facial-image-retake-button" onClick={() => m_navigate( '/' , { replace: true } )}>
										Exit
									</Button>
								</Col>
							</Row>
						</Container>
					</Container>
				</Container>
			</>
		)
	}
	
	function RenderFacialFail()
	{
		//Return element
		return (
			<>
				<Container className="login-facial-status-container">
					<Row>
						<Col className="align-items-center d-flex justify-content-center pb-3">
							<Image src={status_fail} />
						</Col>
					</Row>
					<Row>
						<label className="login-facial-status-text text-center">Fail to Login!</label>
					</Row>
				</Container>

				<Container className="login-facial-retake-container px-3">
					<Row>
						<label className="login-facial-guideline-text text-center">*Ensure that your photo meets the guidelines.</label>
					</Row>
					<Row className="align-items-center d-flex justify-content-center">
						<Button onClick={() => HandleCameraOpen()}>Try Again</Button>
					</Row>
					<Row>
						<Col className="align-items-center d-flex justify-content-center">
							<Button 
								className="register-facial-image-guideline-button align-items-center d-flex justify-content-center" 
								onClick={() => set_guideline_toggle( previous => true )}
							>
								<Stack direction="horizontal" gap={2}>
									<PiWarningCircle color="#0055B8" size={18}/> 
									<label style={{textDecoration: "underline"}}>View Photo Guidelines</label>
								</Stack>
							</Button>
						</Col>
					</Row>
				</Container>
			</>
		)
	}

	function RenderFacialSuccess()
	{
		//Return element
		return (
			<>
				<Container className="login-facial-status-container">
					<Row>
						<Col className="align-items-center d-flex justify-content-center pb-3">
							<Image src={status_success} />
						</Col>
					</Row>
					<Row>
						<label className="login-facial-status-text text-center">Login Success!</label>
					</Row>
				</Container>
			</>
		)
	}

	function RenderFacialGuideline()
	{
		//Return element
		return (
			<>
				<Modal centered onHide={() => set_guideline_toggle( previous => false )} show={m_guideline_toggle}>
					<Modal.Header closeButton style={{ border: 0 , margin: 0 }}></Modal.Header>
					<Modal.Body>
						<Stack gap={2}>
							<Stack className="pb-4" gap={1}>
								<label className="header-text">Guidelines</label>
								<label>Ensure your photo meets the guidelines.</label>
							</Stack>

							<RenderFacialInstructionCorrect />
						</Stack>
					</Modal.Body>
				</Modal>
			</>
		)
	}

	function RenderFacialInstructionCorrect()
	{
		//Return element
		return (
			<Stack className="register-facial-instructions-correct my-2 px-3 py-4" gap={2}>
				<Image className="register-facial-instructions-icon" src={facial_correct_icon} />
				<Stack direction="horizontal" gap={2}>
					<Image src={facial_correct_1} />
					<label>Be in well-lit environment</label>
				</Stack>
				<Stack direction="horizontal" gap={2}>
					<Image src={facial_correct_2} />
					<label>Plain and white background</label>
				</Stack>
				<Stack direction="horizontal" gap={2}>
					<Image src={facial_correct_3} />
					<label>Facial features must be seen in full front view, showing your shoulders and hair</label>
				</Stack>
				<Stack direction="horizontal" gap={2}>
					<Image src={facial_correct_4} />
					<label>Keep a neutral expression with mouth closed</label>
				</Stack>
			</Stack>
		)
	}
	
	if ( m_loading_login ) return <LoginLoading />
	if ( m_image_pass ) return <RenderFacialSuccess />
	return (
		<>
			{	( m_image_retake )
			?	<RenderFacialFail />
			:	<RenderFacialCamera />
			}

			<RenderFacialGuideline />
		</>
	)
}