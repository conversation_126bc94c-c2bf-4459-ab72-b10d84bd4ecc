////////////////////////////////////////////////////////////////////////////////////////////////////
/*!	\file		account_update_payment_stripe.jsx
*	\brief		Account update payment for Stripe page
*	\date		2024
*	\author		daryltan
*	\copyright	Developed for JTC SDD FRP
*	
****************************************************************************************************
*
*	\par	Changelog
*	
*	10 Jul'24 : daryltan - amended card info display
*	2 Jul'24 : daryltan - added card brand logo
*	28 Jun'24 : daryltan - optimised API call to separate file
*	21 Jun'24 : darytltan - added function
*/
////////////////////////////////////////////////////////////////////////////////////////////////////

//Library imports
import { useState } from 'react'
import { Button , Col , Container , Form , Image , Row , Stack } from 'react-bootstrap'
import { IoIosArrowForward } from "react-icons/io";
import { useNavigate } from 'react-router-dom'
import { PulseLoader } from 'react-spinners'
import { PaymentIcon } from 'react-svg-credit-card-payment-icons'
import { PaymentElement , useElements , useStripe } from '@stripe/react-stripe-js'

//Internal imports
import { useCustomerStore } from '../customer_store'
import * as api from '../api/backend'
import { FrontendRoute } from '../data/route';
import AccountUpdate from './account_update'

//Asset imports
import status_fail from '../asset/image/status_fail.png'
import status_success from '../asset/image/status_success.png'

export default function AccountUpdatePaymentStripe()
{
	/* Variables */

	//Constants
	const m_stripe_payment_options =
	{	fields :
		{	billingDetails :
			{	address : 'never'
			}
		}
	,	terms:
		{	card : 'never'
		}
	}

	//Hooks
	const m_navigate = useNavigate()

	//State hooks
	const { session_id , stripe_payment_brand , stripe_payment_expiry , stripe_payment_last4 , setStripePaymentBrand , setStripePaymentExpiry , setStripePaymentLast4 , setStripeSecret } = useCustomerStore()
	const [m_edit , set_edit] = useState( false )
	const [m_loading , set_loading] = useState( false )
	const [m_status , set_status] = useState( false )
	const [m_complete , set_complete] = useState( false )

	//Stripe
	const m_stripe = useStripe()
	const m_stripe_elements = useElements()

	/* Functions */

	function HandleCompleteButton()
	{
		//Navigate to account page
		m_navigate( FrontendRoute.account.session + '/' + String( session_id ) )
	}

	function HandleEditButton()
	{
		//Set edit mode
		set_edit( previous => !m_edit )
	}

	function RenderUpdateForm()
	{
		//Return element
		return (
			<>
				<AccountUpdate />

				<Container className='px-4 pb-3'>
					<Row>
						<label className="login-session-update-body-header-text text-start">Payment Info</label>
					</Row>
				</Container>

				<Form onSubmit={SubmitPaymentForm}>
					<Container className='px-4'>
						{	m_edit
						?	''
						:	<Row className='pb-2'>
								<Col className="align-items-center d-flex justify-content-start">
									<label className="login-session-update-body-input-header">Your number</label>
								</Col>
								<Col className="align-items-center d-flex justify-content-end">
									<Button className="login-session-update-body-edit-button" onClick={HandleEditButton}>
										<Stack className="justify-content-center" direction="horizontal" gap={3}>
											<label>Edit</label>
											<IoIosArrowForward size={18} />
										</Stack>	
									</Button>
								</Col>
							</Row>
						}

						{	m_edit
						?	<Row>
								<PaymentElement options={m_stripe_payment_options} />
							</Row>
						:	<Row className='login-session-update-body-payment-field align-items-center d-flex justify-content-start px-1'>
								<Col className='align-items-center d-flex justify-content-start' xs='auto'>
									<Stack direction='horizontal' gap={2}>
										<PaymentIcon format='flatRounded' type={stripe_payment_brand} />
										Ending in {stripe_payment_last4}
									</Stack>
								</Col>
								<Col className='align-items-center d-flex justify-content-end'>
									{stripe_payment_expiry} { 'amex' === stripe_payment_brand ? '****' : '***' }
								</Col>
							</Row>						
						}
					</Container>
				
					{	m_edit
					?	<Container className="login-session-update-body-button-position">
							<Row className="align-items-center d-flex justify-content-center">
								<Button type="submit">
									Save
								</Button>
							</Row>
						</Container>
					:	''
					}
				</Form>
			</>
		)
	}

	function RenderUpdateLoading()
	{
		//Return element
		return (
			<>
				<Container className='register-loading-container'>
					<Row className='pb-5'>
						<Col className='align-items-center d-flex justify-content-center'>
							<PulseLoader color='#0055B8' loading={true} size={25} speedMultiplier={0.8}/>
						</Col>
					</Row>
					<Row className='align-items-center d-flex justify-content-center'>
						<label className='register-loading-container-text'>Almost there...</label>
					</Row>
				</Container>
			</>
		)
	}

	function RenderUpdateStatus()
	{
		//Return element
		return (
			<>
				<Container className="login-session-update-body-container-position px-3">
					<Row>						
						<Col className="align-items-center d-flex justify-content-center pb-3">
							{	m_status
							?	<Image src={status_success} />
							:	<Image src={status_fail} />
							}
						</Col>
					</Row>
					<Row className="pt-2">
						{	m_status
						?	<label className="login-session-update-body-container-subheader-text text-center">Updated!</label>
						:	<label className="login-session-update-body-container-subheader-text text-center">Failed to update!</label>
						}
					</Row>
					<Row className="pt-3 px-5">
						{	m_status
						?	<label className="login-session-update-body-container-subtext-text text-center">Your payment information has been successfully updated.</label>
						:	<label className="login-session-update-body-container-subtext-text text-center">Your payment information was not updated. Please try again.</label>
						}
					</Row>
				</Container>

				<Container className="login-session-update-body-button-position">
					<Row className="align-items-center d-flex justify-content-center">
						<Button onClick={HandleCompleteButton}>
							{	m_status
							?	'Got it!'
							:	'Retry'
							}
						</Button>
					</Row>
				</Container>
			</>
		)
	}

	async function SubmitPaymentForm( event )
	{

		//Prevent page refresh
		event.preventDefault()
		
		//Confirm setup 
		const temp_response = await m_stripe.confirmSetup
		(	{	confirmParams : 
				{	expand : [ 'payment_method' ]
				,	payment_method_data :
					{	billing_details :
						{	address : { city: null , country: 'SG' , line1: null , line2: null , postal_code: null , state: null }
						}
					}
				,	return_url : ''
				}
			,	elements : m_stripe_elements
			,	redirect : 'if_required'
			}
		)
		
		//Set loader
		set_loading( previous => true )

		//Check for error
		if ( temp_response.error )
		{
			//Log
			console.log( temp_response.error )

			//Set loading
			set_loading( previous => false )

			//Set status
			set_status( previous => false )

			//Set complete
			set_complete( previous => true )
		}
		else
		{
			//Store card info
			setStripePaymentBrand( temp_response.setupIntent.payment_method.card.brand )
			setStripePaymentExpiry
				(	String( temp_response.setupIntent.payment_method.card.exp_month ) 
				+	'/' 
				+	String( temp_response.setupIntent.payment_method.card.exp_year ).slice( -2 ) 
				)
			setStripePaymentLast4( temp_response.setupIntent.payment_method.card.last4 )
			
			//Send to backend
			api.UpdatePayment( { session_id : session_id , stripe_payment : temp_response.setupIntent.payment_method.id } )
			.then
			(	response => 	
				{
					//Set loader
					set_loading( previous => false )
	
					//Set complete
					set_complete( previous => true )
						
					//Check if response is successful
					if ( true === response.data.success )
					{
						//Store stripe secret
						setStripeSecret( response.data.data.stripe.secret )

						//Set status
						set_status( previous => true )
					}
					else
					{
						//Set status
						set_status( previous => false )
					}//end if
				}
			)
			.catch
			(	error =>
				{	
					//Log
					console.log( error )
					
					//Set loader
					set_loading( previous => false )
	
					//Set complete
					set_complete( previous => true )
	
					//Set status
					set_status( previous => false )
				}
			)
		}//end if
	}

	if ( m_loading ) return <RenderUpdateLoading />
	return (
		<>
			{	!m_complete
			?	<RenderUpdateForm />
			:	<RenderUpdateStatus />
			}
		</>
	)
}