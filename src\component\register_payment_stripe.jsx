//Library imports
import { useState } from "react";
import { Button, Container, Form, Modal, Row, Stack } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import {
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";

//Internal imports
import { useCustomerStore } from "../customer_store";
import * as api from "../api/backend";
import { FrontendRoute } from "../data/route";

export default function RegisterPaymentStripe(props) {
  /* Variables */

  //Constants
  //Stripe UI config
  const m_stripe_payment_options = {
    fields: { billingDetails: { address: "never" } },
    terms: { card: "never" },
  };

  //Hooks
  const m_navigate = useNavigate();
  const m_stripe = useStripe();
  const m_stripe_elements = useElements();

  //Global store hooks
  const {
    user_number_mobile,
    stripe_secret,
    setStripeCustomerId,
    setStripePaymentBrand,
    setStripePaymentExpiry,
    setStripePaymentLast4,
  } = useCustomerStore();

  //Alert states
  const [m_alert, set_alert] = useState(false);
  const [m_alert_message, set_alert_message] = useState("");

  /* Functions */

  function RenderPaymentForm() {
    return (
      <Form className="px-2" onSubmit={SubmitPaymentForm}>
        <Container>
          <Row className="pb-4">
            <PaymentElement options={m_stripe_payment_options} />
          </Row>
          <Row className="align-items-center d-flex justify-content-center">
            <Button type="submit">Submit</Button>
          </Row>
        </Container>
      </Form>
    );
  }

  //-----Placeholder
  async function SubmitPaymentForm(event) {
    //Prevent page refresh
    event.preventDefault();

    //Set loading
    props.setLoading(true);

    //Simulate delay and proceed to next step
    if (!m_stripe || !m_stripe_elements || stripe_secret?.startsWith("mock_")) {
      setTimeout(() => {
        setStripePaymentBrand("visa");
        setStripePaymentExpiry("12/26");
        setStripePaymentLast4("4242");
        props.setLoading(false);
        m_navigate(FrontendRoute.register.palm);
      }, 1000);

      return;
    }
  }
  //-----Placeholder End

  /*-----Actual
    async function SubmitPaymentForm(event)
	{
        //Prevent page refresh
		event.preventDefault()

		//Set loading
		props.setLoading(true)

		const temp_response = await m_stripe.confirmSetup
        (   {   confirmParams:
			    {	expand: ['payment_method']
                ,   payment_method_data:
				    {	billing_details:
					    {	address: { city: null, country: 'SG', line1: null, line2: null, postal_code: null, state: null }
					    }
				    }
                ,   return_url: ''
			    }
            ,   elements: m_stripe_elements
            ,   redirect: 'if_required'
		    }
        )

        //Check for error
		if (temp_response.error)
		{
			//Log
            console.log(temp_response.error)

            //Set loading
			props.setLoading(false)

            //Set alert
			set_alert_message(temp_response.error.message)
			set_alert(true)
		}
		else
		{
			//Store card info
			setStripePaymentBrand(temp_response.setupIntent.payment_method.card.brand)
			setStripePaymentExpiry
                (   String(temp_response.setupIntent.payment_method.card.exp_month) 
                +   '/' 
                +   String(temp_response.setupIntent.payment_method.card.exp_year).slice(-2)
			    )
			setStripePaymentLast4(temp_response.setupIntent.payment_method.card.last4)

			//Send to backend
			api.RegisterComplete({   
                number_mobile: user_number_mobile,
                customer_id: stripe_customer_id,
				stripe_payment: temp_response.setupIntent.payment_method.id
			    }
            )
			.then
            (   (response) =>
		        {
                    //Set loading page
                    props.setLoading(false)

                    //Check if successful
			        if (response.data.success)
			        {
				        //Navigate to next page
                        m_navigate(FrontendRoute.register.palm)
			        }
			        else
			        {
                        //Set loading page
                        props.setLoading(false)

                        //Set alert
				        set_alert_message('There was an issue saving your payment method. Please try again.')
				        set_alert(true)
			        }
		        }
            )
			.catch
            (   (error) =>
			    {
				    //Log
                    console.log(error)

                    //Set loading page
				    props.setLoading(false)
			    }
            )
		}
	}
    -----Actual end*/

  /* Main UI */
  return (
    <>
      <RenderPaymentForm />

      <Modal centered onHide={() => set_alert(false)} show={m_alert}>
        <Modal.Header
          closeButton
          style={{ border: 0, margin: 0 }}
        ></Modal.Header>
        <Modal.Body>
          <Stack className="pb-4" gap={1}>
            <label className="header-text">Verify Your Payment Details</label>
            <label>{m_alert_message}</label>
          </Stack>
        </Modal.Body>
      </Modal>
    </>
  );
}
