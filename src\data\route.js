//Internal imports
import * as config from "../config";

//Constants
const backend_api_root =
  process.env.REACT_APP_BACKEND_API_URL + config.backend.route.root;

//Helper functions
const ParseBackendRegisterRoute = (route) => {
  return String(backend_api_root + config.backend.route.register.root + route);
};
const ParseBackendSingpassRoute = (route) => {
  return String(backend_api_root + route);
};
const ParseFrontendRegisterRoute = (route) => {
  return "/" + config.frontend.route.register.root + "/" + route;
};
const ParseFrontendSingpassRoute = (route) => {
  return "/" /* + config.frontend.route.singpass.root + "/" */ + route;
};

//Backend routes
//Export backend route map (to send data to backend)
export const BackendRoute = {
  customer: {
    register: {
      detail: ParseBackendRegisterRoute(config.backend.route.register.detail),
      payment: ParseBackendRegisterRoute(config.backend.route.register.payment),
      otp: ParseBackendRegisterRoute(config.backend.route.register.otp),
      palm: ParseBackendRegisterRoute(config.backend.route.register.palm),
      complete: ParseBackendRegisterRoute(
        config.backend.route.register.complete
      ),
      result: ParseBackendRegisterRoute(config.backend.route.register.result),
    },
  },
  singpass: {
    login: ParseBackendSingpassRoute(config.backend.route.singpass.login),
    callback: ParseBackendSingpassRoute(config.backend.route.singpass.callback),
  },
};

//Frontend routes
//Export frontend route map (to navigate between pages)
export const FrontendRoute = {
  legal: "/" + config.frontend.route.legal.root,
  register: {
    detail: ParseFrontendRegisterRoute(config.frontend.route.register.detail),
    payment: ParseFrontendRegisterRoute(config.frontend.route.register.payment),
    palm: ParseFrontendRegisterRoute(config.frontend.route.register.palm),
    summary: ParseFrontendRegisterRoute(config.frontend.route.register.summary),
  },
  singpass: {
    authenticate: ParseFrontendSingpassRoute(
      config.frontend.route.singpass.callback
    ),
  },
};
